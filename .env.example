# =================================
# 应用配置 (Application Configuration)
# =================================
NODE_ENV=development
PORT=3000

# =================================
# 数据库配置 (Database Configuration)
# =================================
# 请替换为您的 PostgreSQL 连接字符串
# 格式: postgresql://USER:PASSWORD@HOST:PORT/DATABASE
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/snackorder?schema=public"

# =================================
# JWT 密钥配置 (JWT Secret Configuration)
# =================================
# 强烈建议为不同类型的用户使用不同的密钥

# 管理员 JWT
ADMIN_JWT_SECRET="your-super-secret-for-admins"
ADMIN_JWT_EXPIRES_IN="1d"

# 租户 JWT
TENANT_JWT_SECRET="your-super-secret-for-tenants"
TENANT_JWT_EXPIRES_IN="7d"

# 顾客 JWT
CUSTOMER_JWT_SECRET="your-super-secret-for-customers"
CUSTOMER_JWT_EXPIRES_IN="30d"

# =================================
# 短信服务配置 (SMS Service Configuration)
# =================================
# 如果您使用第三方短信服务，请在此处添加配置
# 例如: 阿里云、腾讯云等
# SMS_ACCESS_KEY_ID="your-access-key-id"
# SMS_ACCESS_KEY_SECRET="your-access-key-secret"


# 日志配置
LOG_LEVEL="info"
