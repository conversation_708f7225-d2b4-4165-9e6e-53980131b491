# 更新日志

所有此项目的 notable changes 将被记录在此文件中。

## [1.1.0] - 2025-07-25

### ✨ 新增

- **管理员认证**:
  - 实现了完整的管理员登录认证流程 (`POST /admin/auth/login`)。
  - 登录时通过 `Prisma` 查询 `PlatformAdmin` 数据表验证用户。
  - 使用 `bcrypt` 安全地校验用户密码。
  - 认证成功后生成并返回 JWT (access_token)。
- **数据库种子**:
  - 添加了 `prisma/seed.ts` 脚本，用于初始化第一个平台管理员账号。
  - 默认管理员账号为 `<EMAIL>`，密码为 `Admin123!`。
  - 在 `package.json` 中配置了 `prisma db seed` 命令。
- **日志系统**:
  - 全面优化了 `winston` 日志配置。
  - 控制台日志输出已实现彩色高亮，提升可读性。
  - 错误日志现在会自动记录完整的堆栈信息 (stack trace)。
  - 将日志文件按级别（info/error）和日期进行轮转存储。

### 🐛 修复

- **认证错误处理**:
  - 修复了当用户输入错误密码时，服务器返回 `500 Internal Server Error` 的问题。
  - 现在认证失败会正确地返回 `401 Unauthorized` 状态码及错误信息 `{"message":"邮箱或密码错误","statusCode":401}`。
- **构建与路径解析**:
  - 修复了因 `PrismaClient` 生成路径不一致导致的 `MODULE_NOT_FOUND` 启动错误。
  - 通过在 `tsconfig.json` 中配置路径别名 (`@prisma/client`)，统一了开发、编译和种子脚本中的模块导入。

### ⚙️ 其他

- **CORS**: 确认了 CORS 配置在开发环境下允许所有来源，便于 API 调试。
- **DTO**: 明确了 `AdminLoginDto` 中需要 `password` 字段，而非 `pass`。
