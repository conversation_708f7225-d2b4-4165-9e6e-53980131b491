# Gemini 工作区指南

## 语言

- 使用中文与我交流反馈
- 使用中文编写代码注释和文档
- 系统返回的Response全都使用标准规范的英文，不能使用中文
- 接口返回数据不能使用中文，应使用英文或编号

## 项目概述

本项目是一个基于 NestJS 的 API 服务，为 Web 点餐系统提供多租户支持。

## 主要技术栈

- **框架:** NestJS
- **语言:** TypeScript
- **ORM:** Prisma
- **包管理器:** pnpm

## 常用命令

以下是用于开发和维护本项目的主要命令。

### 安装依赖

要安装所有必需的依赖项，请运行以下命令：

```bash
pnpm install
```

### 运行应用

要启动具有热重载功能的开发服务器，请使用：

```bash
pnpm run start:dev
```

在生产环境中，首先构建应用，然后运行编译后的代码：

```bash
pnpm run build
pnpm run start:prod
```

### 测试

要执行单元测试套件，请运行：

```bash
pnpm run test
```

### 代码检查与格式化

要检查并自动修复代码风格错误，请运行：

```bash
pnpm run lint
```

要根据 Prettier 规则格式化整个代码库，请使用：

```bash
pnpm run format
```

### 数据库迁移

要应用待处理的数据库迁移，请运行以下命令：

```bash
pnpm run prisma:migrate
```
