# 🥡 SnackOrder Backend — NestJS API 服务

基于 NestJS 构建的多租户点餐系统后端服务，支持平台管理、商户管理和顾客端功能。

---

## 🔑 功能概览

- 🎛 **多租户架构**：数据完全隔离（通过 `tenant_id`），支持多商户独立运营
- 👥 **三角色认证**：平台管理员、商户用户（店长/店员）、顾客分离认证
- 🔐 **JWT 认证系统**：基于 Passport 的多策略认证，每种角色独立密钥
- 🧑‍🍳 **商品管理**：支持规格、配料、组合等复杂属性设置（待开发）
- 🛒 **订单处理**：6种状态流转管理（待开发）
- 📱 **短信服务**：支持登录验证、下单验证等场景
- 📄 **API 文档**：Swagger/OpenAPI 自动生成
- 🛠 **数据验证**：class-validator + class-transformer
- 🧾 **日志系统**：Winston + daily rotate 文件

---

## 📦 技术栈

- **框架**：NestJS
- **数据库**：PostgreSQL（生产环境：Vercel Postgres，开发环境：本地 PostgreSQL）
- **ORM**：Prisma
- **认证**：@nestjs/jwt + passport-jwt
- **验证**：class-validator & class-transformer
- **API 文档**：@nestjs/swagger
- **日志记录**：Winston + winston-daily-rotate-file
- **部署**：Vercel

---

## 🚀 快速开始

```bash
git clone https://github.com/CogitoPaw/snackorder-nestjs.git
cd snackorder-nestjs

# 安装依赖
npm install

# 环境配置
cp .env.example .env
# 编辑 .env，设置 DATABASE_URL, JWT_SECRET, PORT 等

# 数据库初始化
npx prisma migrate dev

# 启动开发服务
npm run start:dev
```

## 📁 项目结构

```text
snackorder-nestjs/
├── src/
│   ├── admin/            # 平台管理员模块
│   │   ├── auth/         # 管理员认证
│   │   └── services/     # 管理员服务
│   ├── tenant/           # 商户模块
│   │   ├── auth/         # 商户认证（JWT + 角色管理）
│   │   └── users/        # 商户用户管理（店长/店员）
│   ├── customer/         # 顾客模块
│   │   ├── customer-auth/ # 顾客认证
│   │   └── customers/    # 顾客管理
│   ├── sms/              # 短信服务模块
│   ├── common/           # 公共模块（Prisma、DTOs等）
│   ├── config/           # 配置模块（日志等）
│   ├── main.ts           # 应用启动入口
│   └── app.module.ts     # 根模块
├── prisma/
│   ├── schema.prisma     # 数据库模型
│   ├── migrations/       # 数据库迁移
│   └── seed.ts          # 种子数据
├── docs/                 # 文档目录
├── .env.example
├── package.json
└── README.md
```

## 🏗 系统架构

### 多租户设计模式

- **数据隔离**：所有业务数据通过 `tenantId` 字段实现行级隔离
- **认证分离**：平台管理员、商户用户、顾客使用不同的JWT策略
- **模块化设计**：每个角色拥有独立的模块和路由

### 认证策略

- `admin-jwt`：平台管理员认证
- `tenant-jwt`：商户用户认证（店长/店员）
- `customer-jwt`：顾客认证

## 🗺 开发阶段规划

### 阶段 1：基础设施 🏗️ ✅

- [x] 项目初始化和依赖配置
- [x] 数据库模型设计（多租户架构）
- [x] 基础配置和环境变量

### 阶段 2：核心认证 🔐 ✅

- [x] JWT 认证系统（三种策略）
- [x] 用户角色管理（店长/店员）
- [x] 租户管理模块
- [x] 短信服务集成

### 阶段 3：业务逻辑 🛒 (进行中)

- [ ] 商品分类管理
- [ ] 商品管理（含复杂属性：规格、配料、组合）
- [ ] 订单系统（6种状态流转）
- [ ] 库存管理

### 阶段 4：完善优化 ✨

- [ ] 支付系统集成
- [ ] 打印服务
- [ ] 数据统计和报表
- [ ] 性能优化
- [ ] 单元测试和集成测试

## 💳 支付集成计划

当前版本不包含支付功能，后期计划集成 Stripe 支付。

## 🏢 多租户设计

### 数据隔离策略

每个商户的数据完全独立：

- **数据库隔离**：通过 `tenantId` 字段实现行级数据隔离
- **用户体系**：店长、店员账号与租户绑定
- **业务数据**：商品、订单、顾客等数据按租户隔离
- **配置独立**：营业时间、配送费等个性化设置

### 认证机制

- **JWT载荷包含租户信息**：确保跨租户访问控制
- **角色权限分离**：店长和店员拥有不同的操作权限
- **顾客数据隔离**：顾客只能访问对应租户的服务

## 📋 订单状态流程

```text
待支付 → 已支付 → 制作中 → 已完成
   ↓        ↓        ↓
 已取消   已取消   已退款
```
