# 管理员商户管理 API 文档

## 概述

管理员商户管理API提供了完整的商户生命周期管理功能，包括商户的创建、查询、更新、删除以及店长账号的初始化。

## 认证要求

所有API接口都需要管理员认证，请在请求头中包含有效的JWT令牌：

```text
Authorization: Bearer <admin_jwt_token>
```

## API 接口

### 1. 创建商户

**POST** `/admin/tenants`

创建新的商户账号。

#### 请求体

```json
{
  "name": "商户名称",
  "slug": "tenant-slug",
  "description": "商户描述（可选）",
  "logo": "https://example.com/logo.png（可选）",
  "phone": "13800138000（可选）",
  "address": "商户地址（可选）",
  "status": "ACTIVE"
}
```

#### 响应

```json
{
  "id": "tenant_123",
  "name": "商户名称",
  "slug": "tenant-slug",
  "description": "商户描述",
  "logo": "https://example.com/logo.png",
  "phone": "13800138000",
  "address": "商户地址",
  "status": "ACTIVE",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z",
  "manager": null
}
```

#### 错误响应

- `409 Conflict` - 路由标识符已存在

### 2. 获取商户列表

**GET** `/admin/tenants`

获取商户列表，支持分页和筛选。

#### 查询参数

- `page` (可选) - 页码，默认为1
- `limit` (可选) - 每页数量，默认为10
- `search` (可选) - 搜索关键词，匹配商户名称
- `status` (可选) - 商户状态筛选

#### 响应

```json
{
  "data": [
    {
      "id": "tenant_123",
      "name": "商户名称",
      "slug": "tenant-slug",
      "description": "商户描述",
      "logo": "https://example.com/logo.png",
      "phone": "13800138000",
      "address": "商户地址",
      "status": "ACTIVE",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "manager": {
        "id": "manager_456",
        "email": "<EMAIL>",
        "name": "店长姓名",
        "lastLoginAt": "2024-01-01T00:00:00.000Z"
      }
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10,
  "totalPages": 1
}
```

### 3. 获取商户详情

**GET** `/admin/tenants/:id`

获取指定商户的详细信息。

#### 路径参数

- `id` - 商户ID

#### 响应

```json
{
  "id": "tenant_123",
  "name": "商户名称",
  "slug": "tenant-slug",
  "description": "商户描述",
  "logo": "https://example.com/logo.png",
  "phone": "13800138000",
  "address": "商户地址",
  "status": "ACTIVE",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z",
  "manager": {
    "id": "manager_456",
    "email": "<EMAIL>",
    "name": "店长姓名",
    "lastLoginAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 错误响应

- `404 Not Found` - 商户不存在

### 4. 更新商户信息

**PUT** `/admin/tenants/:id`

更新指定商户的信息。

#### 路径参数

- `id` - 商户ID

#### 请求体

```json
{
  "name": "更新的商户名称（可选）",
  "description": "更新的商户描述（可选）",
  "logo": "https://example.com/new-logo.png（可选）",
  "phone": "13900139000（可选）",
  "address": "新的商户地址（可选）",
  "status": "INACTIVE（可选）"
}
```

#### 响应

```json
{
  "id": "tenant_123",
  "name": "更新的商户名称",
  "slug": "tenant-slug",
  "description": "更新的商户描述",
  "logo": "https://example.com/new-logo.png",
  "phone": "13900139000",
  "address": "新的商户地址",
  "status": "INACTIVE",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T12:00:00.000Z",
  "manager": {
    "id": "manager_456",
    "email": "<EMAIL>",
    "name": "店长姓名",
    "lastLoginAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 错误响应

- `404 Not Found` - 商户不存在
- `409 Conflict` - 路由标识符已存在（当更新slug时）

### 5. 删除商户

**DELETE** `/admin/tenants/:id`

软删除指定商户（将状态设置为SUSPENDED）。

#### 路径参数

- `id` - 商户ID

#### 响应

- `204 No Content` - 删除成功

#### 错误响应

- `404 Not Found` - 商户不存在

### 6. 初始化商户店长

**POST** `/admin/tenants/:id/initialize-manager`

为指定商户创建店长账号。

#### 路径参数

- `id` - 商户ID

#### 请求体

```json
{
  "email": "<EMAIL>",
  "name": "店长姓名",
  "password": "初始密码（可选，不提供将自动生成）"
}
```

#### 响应

```json
{
  "success": true,
  "managerId": "manager_456",
  "email": "<EMAIL>",
  "initialPassword": "abcd1234",
  "message": "店长账号初始化成功"
}
```

#### 错误响应

- `404 Not Found` - 商户不存在
- `409 Conflict` - 该商户已有店长账号或邮箱已被使用

### 7. 健康检查

**GET** `/admin/tenants/test/health`

检查商户管理服务的运行状态。

#### 响应

```json
{
  "message": "管理员商户服务运行正常",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 状态码说明

- `200 OK` - 请求成功
- `201 Created` - 资源创建成功
- `204 No Content` - 删除成功
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 权限不足
- `404 Not Found` - 资源不存在
- `409 Conflict` - 资源冲突
- `500 Internal Server Error` - 服务器内部错误

## 商户状态说明

- `ACTIVE` - 正常营业
- `INACTIVE` - 暂停营业
- `SUSPENDED` - 被平台暂停

## 使用示例

### 创建商户并初始化店长

```bash
# 1. 创建商户
curl -X POST http://localhost:3000/admin/tenants \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试餐厅",
    "slug": "test-restaurant",
    "description": "一家测试餐厅",
    "phone": "13800138000",
    "address": "测试地址123号"
  }'

# 2. 初始化店长
curl -X POST http://localhost:3000/admin/tenants/TENANT_ID/initialize-manager \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "张店长"
  }'
```

### 查询商户列表

```bash
curl -X GET "http://localhost:3000/admin/tenants?page=1&limit=10&search=测试&status=ACTIVE" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 注意事项

1. 所有接口都需要管理员权限认证
2. 商户的slug（路由标识符）必须全局唯一
3. 每个商户只能有一个店长账号
4. 删除商户为软删除，不会真正删除数据
5. 初始化店长时如果不提供密码，系统会自动生成8位随机密码
6. 店长邮箱在整个系统中必须唯一
