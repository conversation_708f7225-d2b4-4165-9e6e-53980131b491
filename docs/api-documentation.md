# SnackOrder API 文档

## 1. 通用规范

### 1.1 基础信息

- **API 版本**: v1
- **基础 URL**: `https://api.snackorder.com/so-api/v1`
- **协议**: HTTPS
- **数据格式**: JSON

### 1.2 通用请求头

```text
Content-Type: application/json
Authorization: Bearer {jwt_token}
Accept-Language: zh-CN,en-US
```

### 1.3 通用响应格式

#### 成功响应

```json
{
  "success": true,
  "data": {},
  "message": "Request completed successfully",
  "code": 200,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid username or password",
    "details": [
      {
        "field": "email",
        "message": "Email format is invalid",
        "value": "invalid-email"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 1.4 分页响应格式

```json
{
  "success": true,
  "data": {
    "data": [],
    "total": 100,
    "page": 1,
    "limit": 20,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  },
  "message": "Request completed successfully",
  "code": 200,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 2. 错误代码说明

### 2.1 通用错误代码 (1000-3999)

| 错误代码 | HTTP状态码 | 说明 |
|---------|-----------|------|
| INTERNAL_SERVER_ERROR | 500 | 服务器内部错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| VALIDATION_ERROR | 400 | 数据验证失败 |
| INVALID_REQUEST_FORMAT | 400 | 请求格式无效 |
| AUTHENTICATION_FAILED | 401 | 认证失败 |
| INVALID_TOKEN | 401 | 无效的令牌 |
| TOKEN_EXPIRED | 401 | 令牌已过期 |
| INSUFFICIENT_PERMISSIONS | 403 | 权限不足 |
| RESOURCE_NOT_FOUND | 404 | 资源不存在 |
| RESOURCE_ALREADY_EXISTS | 409 | 资源已存在 |

### 2.2 业务错误代码 (5000-9999)

| 错误代码 | HTTP状态码 | 说明 |
|---------|-----------|------|
| TENANT_NOT_FOUND | 404 | 租户不存在 |
| TENANT_INACTIVE | 400 | 租户未激活 |
| TENANT_SUSPENDED | 400 | 租户已暂停 |
| USER_NOT_FOUND | 404 | 用户不存在 |
| USER_ALREADY_EXISTS | 409 | 用户已存在 |
| PRODUCT_NOT_FOUND | 404 | 商品不存在 |
| PRODUCT_OUT_OF_STOCK | 400 | 商品库存不足 |
| ORDER_NOT_FOUND | 404 | 订单不存在 |
| ORDER_ALREADY_PAID | 400 | 订单已支付 |

## 3. 认证机制

### 3.1 JWT 认证

系统使用 JWT (JSON Web Token) 进行身份认证，支持三种角色：

1. **平台管理员** (`admin-jwt`)
2. **商户用户** (`tenant-jwt`) 
3. **顾客** (`customer-jwt`)

### 3.2 获取访问令牌

#### 平台管理员登录

```http
POST /admin/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 商户用户登录

```http
POST /tenant/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 顾客认证（短信验证码）

```http
POST /customer/auth/verify-sms
Content-Type: application/json

{
  "phone": "+8613800138000",
  "code": "123456",
  "tenantId": "tenant_123"
}
```

### 3.3 使用访问令牌

在所有需要认证的请求中，在请求头中包含访问令牌：

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 4. 限流规则

### 4.1 全局限流

- **窗口时间**: 15分钟
- **请求限制**: 每IP 1000次请求

### 4.2 认证接口限流

- **窗口时间**: 15分钟  
- **请求限制**: 每IP 10次登录尝试

### 4.3 短信接口限流

- **窗口时间**: 1分钟
- **请求限制**: 每手机号 1次发送

## 5. 数据验证规则

### 5.1 通用验证规则

- **字符串长度**: 最大 255 字符
- **数字范围**: 根据业务需求设定
- **必填字段**: 在API文档中明确标注

### 5.2 特殊字段验证

- **邮箱**: 符合 RFC 5322 标准
- **手机号**: 中国大陆手机号格式 (+86)
- **密码**: 最少8位，包含字母和数字
- **UUID**: 标准 UUID v4 格式

## 6. 环境说明

### 6.1 开发环境

- **基础URL**: `http://localhost:3000/so-api`
- **文档地址**: `http://localhost:3000/docs`

### 6.2 生产环境

- **基础URL**: `https://api.snackorder.com/so-api`
- **文档地址**: `https://api.snackorder.com/docs`

## 7. API 端点概览

### 7.1 平台管理员 API

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/admin/auth/login` | POST | 管理员登录 | 无 |
| `/admin/auth/profile` | GET | 获取管理员信息 | admin-jwt |
| `/admin/tenants` | GET | 获取租户列表 | admin-jwt |
| `/admin/tenants` | POST | 创建租户 | admin-jwt |
| `/admin/tenants/{id}` | GET | 获取租户详情 | admin-jwt |
| `/admin/tenants/{id}` | PUT | 更新租户信息 | admin-jwt |
| `/admin/tenants/{id}` | DELETE | 删除租户 | admin-jwt |

### 7.2 商户管理 API

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/tenant/auth/login` | POST | 商户用户登录 | 无 |
| `/tenant/auth/profile` | GET | 获取用户信息 | tenant-jwt |
| `/tenant/users` | GET | 获取用户列表 | tenant-jwt |
| `/tenant/users` | POST | 创建用户 | tenant-jwt |
| `/tenant/categories` | GET | 获取商品分类 | tenant-jwt |
| `/tenant/categories` | POST | 创建商品分类 | tenant-jwt |
| `/tenant/products` | GET | 获取商品列表 | tenant-jwt |
| `/tenant/products` | POST | 创建商品 | tenant-jwt |
| `/tenant/orders` | GET | 获取订单列表 | tenant-jwt |
| `/tenant/orders/{id}` | GET | 获取订单详情 | tenant-jwt |

### 7.3 顾客端 API

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/customer/auth/send-sms` | POST | 发送短信验证码 | 无 |
| `/customer/auth/verify-sms` | POST | 验证短信登录 | 无 |
| `/customer/store/{tenantId}/products` | GET | 获取店铺商品 | 无 |
| `/customer/store/{tenantId}/categories` | GET | 获取店铺分类 | 无 |
| `/customer/orders` | POST | 创建订单 | customer-jwt |
| `/customer/orders` | GET | 获取订单历史 | customer-jwt |

## 8. 请求示例

### 8.1 创建商品分类

```http
POST /tenant/categories
Authorization: Bearer {tenant_jwt_token}
Content-Type: application/json

{
  "name": "Hot Drinks",
  "sort": 1
}
```

**响应示例:**

```json
{
  "success": true,
  "data": {
    "id": "cat_123456",
    "name": "Hot Drinks",
    "sort": 1,
    "status": "ACTIVE",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "Resource created successfully",
  "code": 201,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 8.2 获取商品列表（分页）

```http
GET /tenant/products?page=1&limit=20&categoryId=cat_123456&status=ACTIVE
Authorization: Bearer {tenant_jwt_token}
```

**响应示例:**

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "prod_123456",
        "name": "Americano",
        "description": "Classic black coffee",
        "price": 25.00,
        "image": "https://example.com/americano.jpg",
        "status": "ACTIVE",
        "category": {
          "id": "cat_123456",
          "name": "Hot Drinks"
        }
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  },
  "message": "Request completed successfully",
  "code": 200,
  "timestamp": "2024-01-01T00:00:00Z"
}
```
