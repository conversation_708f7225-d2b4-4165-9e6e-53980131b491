# 餐饮订餐系统数据库操作指南

## 1. 数据库架构概览

本系统采用多租户架构，支持多个商户（餐厅）独立运营，数据完全隔离。

### 1.1 核心设计理念

- **多租户隔离**: 每个商户的数据通过 `tenantId` 完全隔离
- **历史数据保护**: 订单、支付等关键数据采用快照机制保护历史记录
- **数据完整性**: 通过外键约束和级联删除策略确保数据一致性
- **性能优化**: 合理的索引设计支持高并发查询

## 2. 数据表结构详解

### 2.1 平台层面

#### PlatformAdmin (平台管理员)

```sql
-- 平台超级管理员，管理所有租户
CREATE TABLE platform_admins (
  id TEXT PRIMARY KEY,           -- 主键ID
  email TEXT UNIQUE NOT NULL,    -- 邮箱地址（唯一）
  password TEXT NOT NULL,        -- 密码hash
  name TEXT NOT NULL,            -- 管理员姓名
  status TEXT NOT NULL DEFAULT 'ACTIVE', -- 账号状态：ACTIVE/INACTIVE
  last_login_at TIMESTAMP,       -- 最后登录时间
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2.2 租户层面

#### Tenant (商户信息)

```sql
-- 商户/餐厅基本信息
CREATE TABLE tenants (
  id TEXT PRIMARY KEY,           -- 主键ID
  name TEXT NOT NULL,            -- 商户名称
  slug TEXT UNIQUE NOT NULL,     -- 路由标识符（如: "kfc-wangfujing"）
  description TEXT,              -- 商户描述
  logo TEXT,                     -- 商户LOGO
  phone TEXT,                    -- 商户联系电话
  address TEXT,                  -- 商户地址
  status TEXT NOT NULL DEFAULT 'ACTIVE', -- 商户状态：ACTIVE/INACTIVE/SUSPENDED
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### TenantSetting (商户设置)

```sql
-- 商户个性化配置（营业时间、配送费等）
CREATE TABLE tenant_settings (
  id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL,       -- 关联租户
  key TEXT NOT NULL,             -- 设置键名（如："business_hours", "delivery_fee"）
  value TEXT NOT NULL,           -- 设置值（JSON格式）
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(tenant_id, key)
);
```

### 2.3 用户系统

#### User (商户员工)

```sql
-- 商户员工账号（店长、店员）
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL,       -- 租户ID（数据隔离）
  email TEXT NOT NULL,           -- 邮箱地址
  password TEXT NOT NULL,        -- 密码hash
  name TEXT NOT NULL,            -- 员工姓名
  role TEXT NOT NULL,            -- 角色：MANAGER/STAFF
  status TEXT NOT NULL DEFAULT 'ACTIVE',
  last_login_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(email, tenant_id)       -- 同一租户下邮箱唯一
);
```

#### Customer (客户信息)

```sql
-- 客户信息及CRM数据
CREATE TABLE customers (
  id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL,       -- 租户ID
  phone TEXT NOT NULL,           -- 手机号（主要标识）
  name TEXT,                     -- 姓名（可选）
  level TEXT NOT NULL DEFAULT 'NORMAL', -- 客户等级：NORMAL/VIP/DIAMOND/BLACKLIST
  last_order_at TIMESTAMP,       -- 最后下单时间
  total_orders INTEGER DEFAULT 0, -- 订单总数
  total_amount DECIMAL(10,2) DEFAULT 0, -- 消费总额
  tags TEXT[],                   -- CRM标签数组
  notes TEXT,                    -- 客户备注
  birthday TIMESTAMP,            -- 生日
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(phone, tenant_id)       -- 同一租户下手机号唯一
);
```

### 2.4 商品系统

#### Category (商品分类)

```sql
-- 商品分类管理
CREATE TABLE categories (
  id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL,       -- 租户ID
  name TEXT NOT NULL,            -- 分类名称
  sort INTEGER DEFAULT 0,        -- 排序权重
  status TEXT NOT NULL DEFAULT 'ACTIVE',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Product (商品信息)

```sql
-- 商品基本信息
CREATE TABLE products (
  id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL,       -- 租户ID
  category_id TEXT,              -- 分类ID（可选）
  name TEXT NOT NULL,            -- 商品名称
  description TEXT,              -- 商品描述
  price DECIMAL(10,2) NOT NULL,  -- 基础价格
  image TEXT,                    -- 商品图片
  stock INTEGER,                 -- 库存数量（null表示不限库存）
  sort INTEGER DEFAULT 0,        -- 排序权重
  status TEXT NOT NULL DEFAULT 'ACTIVE',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### ProductAttribute (商品属性)

```sql
-- 商品属性定义（规格、配料等）
CREATE TABLE product_attributes (
  id TEXT PRIMARY KEY,
  product_id TEXT NOT NULL,      -- 商品ID
  type TEXT NOT NULL,            -- 属性类型：SIZE/INGREDIENT/COMBO/ADDON
  name TEXT NOT NULL,            -- 属性名称（如"杯型"、"甜度"）
  required BOOLEAN DEFAULT FALSE, -- 是否必选
  multiple BOOLEAN DEFAULT FALSE, -- 是否可多选
  sort INTEGER DEFAULT 0,        -- 排序权重
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### ProductAttributeOption (商品属性选项)

```sql
-- 商品属性的具体选项值
CREATE TABLE product_attribute_options (
  id TEXT PRIMARY KEY,
  attribute_id TEXT NOT NULL,    -- 属性ID
  name TEXT NOT NULL,            -- 选项名称（如"大杯"、"无糖"）
  price DECIMAL(10,2) DEFAULT 0, -- 附加价格
  sort INTEGER DEFAULT 0,        -- 排序权重
  status TEXT NOT NULL DEFAULT 'ACTIVE',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2.5 订单系统

#### Order (订单主表)

```sql
-- 客户订单主表
CREATE TABLE orders (
  id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL,       -- 租户ID
  customer_id TEXT NOT NULL,     -- 客户ID
  order_number TEXT UNIQUE NOT NULL, -- 订单号（全局唯一）
  source TEXT NOT NULL DEFAULT 'MINIPROGRAM', -- 订单来源：MINIPROGRAM/H5/APP/POS
  total_amount DECIMAL(10,2) NOT NULL, -- 订单总金额
  status TEXT NOT NULL DEFAULT 'PENDING_PAYMENT', -- 订单状态
  payment_status TEXT NOT NULL DEFAULT 'UNPAID', -- 支付状态
  note TEXT,                     -- 订单备注
  handled_by TEXT,               -- 处理人员ID
  paid_at TIMESTAMP,             -- 支付时间
  completed_at TIMESTAMP,        -- 完成时间
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### OrderItem (订单商品项)

```sql
-- 订单中的具体商品项
CREATE TABLE order_items (
  id TEXT PRIMARY KEY,
  order_id TEXT NOT NULL,        -- 订单ID
  product_id TEXT NOT NULL,      -- 商品ID
  product_name TEXT NOT NULL,    -- 商品名称快照
  quantity INTEGER DEFAULT 1,    -- 商品数量
  unit_price DECIMAL(10,2) NOT NULL, -- 商品单价快照
  total_price DECIMAL(10,2) NOT NULL, -- 小计金额
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### OrderItemAttributeOption (订单商品属性选项)

```sql
-- 订单商品的选择选项记录
CREATE TABLE order_item_attribute_options (
  id TEXT PRIMARY KEY,
  order_item_id TEXT NOT NULL,   -- 订单商品项ID
  option_id TEXT NOT NULL,       -- 属性选项ID
  option_name TEXT NOT NULL,     -- 选项名称快照
  price DECIMAL(10,2) NOT NULL,  -- 选项价格快照
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(order_item_id, option_id)
);
```

### 2.6 支付系统

#### Payment (支付记录)

```sql
-- 订单支付流水记录
CREATE TABLE payments (
  id TEXT PRIMARY KEY,
  order_id TEXT NOT NULL,        -- 订单ID（支持多次支付）
  amount DECIMAL(10,2) NOT NULL, -- 支付金额
  method TEXT NOT NULL,          -- 支付方式：WECHAT/ALIPAY/CASH/CARD
  status TEXT NOT NULL DEFAULT 'UNPAID', -- 支付状态
  third_party_id TEXT,           -- 第三方支付平台交易ID
  paid_at TIMESTAMP,             -- 支付完成时间
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2.7 辅助系统

#### SmsCode (短信验证码)

```sql
-- 短信验证码记录
CREATE TABLE sms_codes (
  id TEXT PRIMARY KEY,
  phone TEXT NOT NULL,           -- 手机号
  code TEXT NOT NULL,            -- 验证码
  type TEXT NOT NULL,            -- 验证码类型：LOGIN/ORDER/PAYMENT
  used BOOLEAN DEFAULT FALSE,    -- 是否已使用
  expires_at TIMESTAMP NOT NULL, -- 过期时间
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### PrintJob (打印任务)

```sql
-- 小票打印队列管理
CREATE TABLE print_jobs (
  id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL,       -- 租户ID
  order_id TEXT NOT NULL,        -- 订单ID
  type TEXT NOT NULL,            -- 打印类型：ORDER_RECEIPT/PAYMENT_RECEIPT/COMPLETION_RECEIPT
  status TEXT NOT NULL DEFAULT 'PENDING', -- 打印状态：PENDING/PRINTED/FAILED
  retry_count INTEGER DEFAULT 0, -- 重试次数
  printed_at TIMESTAMP,          -- 打印完成时间
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 3. 重要的外键约束和级联策略

### 3.1 保护历史数据的策略

```sql
-- 客户删除时，历史订单不能被删除
ALTER TABLE orders ADD CONSTRAINT fk_orders_customer
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT;

-- 商品删除时，历史订单项不能被删除
ALTER TABLE order_items ADD CONSTRAINT fk_order_items_product
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT;

-- 属性选项删除时，历史订单选项不能被删除
ALTER TABLE order_item_attribute_options ADD CONSTRAINT fk_order_item_options_option
  FOREIGN KEY (option_id) REFERENCES product_attribute_options(id) ON DELETE RESTRICT;
```

### 3.2 合理的级联删除策略

```sql
-- 员工删除时，相关订单的处理人设为NULL
ALTER TABLE orders ADD CONSTRAINT fk_orders_handler
  FOREIGN KEY (handled_by) REFERENCES users(id) ON DELETE SET NULL;

-- 分类删除时，商品的分类ID设为NULL
ALTER TABLE products ADD CONSTRAINT fk_products_category
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL;

-- 租户删除时，相关数据全部删除
ALTER TABLE users ADD CONSTRAINT fk_users_tenant
  FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
```

## 4. 关键索引配置

### 4.1 查询性能优化索引

```sql
-- 租户数据隔离相关
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_customers_tenant_id ON customers(tenant_id);
CREATE INDEX idx_products_tenant_id ON products(tenant_id);
CREATE INDEX idx_categories_tenant_id ON categories(tenant_id);
CREATE INDEX idx_orders_tenant_id_status ON orders(tenant_id, status);

-- 登录认证相关
CREATE INDEX idx_platform_admins_email ON platform_admins(email);
CREATE INDEX idx_users_email ON users(email);

-- 客户查询相关
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_level ON customers(level);

-- 订单查询相关
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);

-- 支付查询相关
CREATE INDEX idx_payments_order_id ON payments(order_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_third_party_id ON payments(third_party_id);

-- 短信验证码查询优化
CREATE INDEX idx_sms_codes_phone_type_used ON sms_codes(phone, type, used);
CREATE INDEX idx_sms_codes_expires_at ON sms_codes(expires_at);

-- 打印任务查询优化
CREATE INDEX idx_print_jobs_tenant_status ON print_jobs(tenant_id, status);
CREATE INDEX idx_print_jobs_order_id ON print_jobs(order_id);
```

## 5. 常用操作指南

### 5.1 租户数据初始化

```sql
-- 1. 创建新租户
INSERT INTO tenants (id, name, slug, status)
VALUES ('tenant_001', '星巴克王府井店', 'starbucks-wangfujing', 'ACTIVE');

-- 2. 创建店长账号
INSERT INTO users (id, tenant_id, email, password, name, role)
VALUES ('user_001', 'tenant_001', '<EMAIL>', 'hashed_password', '张店长', 'MANAGER');

-- 3. 设置商户配置
INSERT INTO tenant_settings (id, tenant_id, key, value) VALUES
('set_001', 'tenant_001', 'business_hours', '{"open": "07:00", "close": "22:00"}'),
('set_002', 'tenant_001', 'delivery_fee', '{"fee": 5.00, "free_threshold": 30.00}');
```

### 5.2 商品管理操作

```sql
-- 1. 创建商品分类
INSERT INTO categories (id, tenant_id, name, sort)
VALUES ('cat_001', 'tenant_001', '咖啡饮品', 1);

-- 2. 创建商品
INSERT INTO products (id, tenant_id, category_id, name, price, status)
VALUES ('prod_001', 'tenant_001', 'cat_001', '美式咖啡', 28.00, 'ACTIVE');

-- 3. 添加商品属性（杯型）
INSERT INTO product_attributes (id, product_id, type, name, required)
VALUES ('attr_001', 'prod_001', 'SIZE', '杯型', TRUE);

-- 4. 添加属性选项
INSERT INTO product_attribute_options (id, attribute_id, name, price) VALUES
('opt_001', 'attr_001', '中杯', 0.00),
('opt_002', 'attr_001', '大杯', 3.00),
('opt_003', 'attr_001', '超大杯', 6.00);
```

### 5.3 订单处理流程

```sql
-- 1. 创建客户（如不存在）
INSERT INTO customers (id, tenant_id, phone, name)
VALUES ('cust_001', 'tenant_001', '13800138000', '张三')
ON CONFLICT (phone, tenant_id) DO NOTHING;

-- 2. 创建订单
INSERT INTO orders (id, tenant_id, customer_id, order_number, total_amount, source)
VALUES ('order_001', 'tenant_001', 'cust_001', 'ORD20240101001', 31.00, 'MINIPROGRAM');

-- 3. 添加订单商品项（含快照数据）
INSERT INTO order_items (id, order_id, product_id, product_name, quantity, unit_price, total_price)
VALUES ('item_001', 'order_001', 'prod_001', '美式咖啡', 1, 28.00, 28.00);

-- 4. 添加商品选项（含快照数据）
INSERT INTO order_item_attribute_options (id, order_item_id, option_id, option_name, price)
VALUES ('item_opt_001', 'item_001', 'opt_002', '大杯', 3.00);

-- 5. 创建支付记录
INSERT INTO payments (id, order_id, amount, method, status)
VALUES ('pay_001', 'order_001', 31.00, 'WECHAT', 'PAID');

-- 6. 更新订单状态
UPDATE orders SET
  status = 'PAID',
  payment_status = 'PAID',
  paid_at = NOW()
WHERE id = 'order_001';
```

## 6. 数据维护和清理

### 6.1 定期清理过期数据

```sql
-- 清理过期的短信验证码（每天执行）
DELETE FROM sms_codes WHERE expires_at < NOW() - INTERVAL '1 day';

-- 清理旧的打印任务记录（保留30天）
DELETE FROM print_jobs WHERE created_at < NOW() - INTERVAL '30 days' AND status = 'PRINTED';
```

### 6.2 数据统计查询

```sql
-- 租户营业数据统计
SELECT
  t.name AS tenant_name,
  COUNT(o.id) AS total_orders,
  SUM(o.total_amount) AS total_revenue,
  COUNT(DISTINCT o.customer_id) AS unique_customers
FROM tenants t
LEFT JOIN orders o ON t.id = o.tenant_id AND o.status = 'COMPLETED'
WHERE o.created_at >= '2024-01-01'
GROUP BY t.id, t.name;

-- 商品销售排行
SELECT
  p.name AS product_name,
  SUM(oi.quantity) AS total_sold,
  SUM(oi.total_price) AS total_revenue
FROM products p
JOIN order_items oi ON p.id = oi.product_id
JOIN orders o ON oi.order_id = o.id
WHERE o.status = 'COMPLETED' AND o.created_at >= '2024-01-01'
GROUP BY p.id, p.name
ORDER BY total_sold DESC;
```

## 7. 性能监控建议

### 7.1 慢查询监控

定期检查执行时间超过1秒的查询，特别关注：

- 跨租户的数据查询（应该避免）
- 缺少索引的关联查询
- 大量数据的聚合统计查询

### 7.2 索引使用率监控

定期分析索引使用情况：

```sql
-- PostgreSQL 索引使用率查询示例
SELECT
  schemaname,
  tablename,
  indexname,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_tup_read DESC;
```

## 8. 软删除机制

### 8.1 支持软删除的模型

以下核心业务模型支持软删除（添加了 `deletedAt` 字段）：

- **Tenant** - 商户信息，误删影响巨大
- **User** - 员工账号，可能需要恢复
- **Customer** - 客户信息，涉及历史订单数据
- **Category** - 商品分类，可能临时隐藏后恢复
- **Product** - 商品信息，下架商品可能重新上架

### 8.2 软删除操作示例

```sql
-- 软删除商户（标记为删除，但保留数据）
UPDATE tenants SET deleted_at = NOW() WHERE id = 'tenant_001';

-- 恢复软删除的商户
UPDATE tenants SET deleted_at = NULL WHERE id = 'tenant_001';

-- 查询未删除的商户
SELECT * FROM tenants WHERE deleted_at IS NULL;

-- 查询已删除的商户（管理员功能）
SELECT * FROM tenants WHERE deleted_at IS NOT NULL;
```

### 8.3 软删除查询注意事项

1. **业务查询必须过滤**: 所有业务查询都需要添加 `WHERE deleted_at IS NULL`
2. **唯一约束处理**: 软删除后，相同数据可以重新创建（如相同手机号的客户）
3. **关联数据处理**: 软删除的数据其关联数据仍然有效，查询时需特别注意
4. **定期清理**: 建议定期清理长期未恢复的软删除数据

### 8.4 Prisma 中的软删除实现

```typescript
// 查询未删除的商户
const activeTenants = await prisma.tenant.findMany({
  where: {
    deletedAt: null
  }
});

// 软删除商户
await prisma.tenant.update({
  where: { id: 'tenant_001' },
  data: { deletedAt: new Date() }
});

// 恢复软删除的商户
await prisma.tenant.update({
  where: { id: 'tenant_001' },
  data: { deletedAt: null }
});
```

## 9. 索引优化策略

### 9.1 索引设计原则

1. **避免冗余索引**: `@unique` 字段无需额外的 `@@index`
2. **复合索引优先**: `@@index([a, b])` 可覆盖对字段 `a` 的查询
3. **软删除索引**: 为 `deletedAt` 字段添加索引，优化过滤查询
4. **查询模式驱动**: 根据实际查询模式设计索引

### 9.2 当前索引配置

```sql
-- 租户相关索引
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_deleted_at ON tenants(deleted_at);

-- 用户相关索引（复合唯一索引已覆盖 tenant_id 查询）
CREATE UNIQUE INDEX idx_users_email_tenant ON users(email, tenant_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

-- 客户相关索引（复合唯一索引已覆盖 phone 和 tenant_id 查询）
CREATE UNIQUE INDEX idx_customers_phone_tenant ON customers(phone, tenant_id);
CREATE INDEX idx_customers_level ON customers(level);
CREATE INDEX idx_customers_deleted_at ON customers(deleted_at);

-- 商品相关索引
CREATE INDEX idx_categories_tenant_status ON categories(tenant_id, status);
CREATE INDEX idx_categories_deleted_at ON categories(deleted_at);
CREATE INDEX idx_products_tenant_status ON products(tenant_id, status);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_deleted_at ON products(deleted_at);
```

## 10. 安全注意事项

1. **数据隔离**: 所有业务查询必须包含 `tenant_id` 条件
2. **软删除过滤**: 业务查询必须过滤 `deleted_at IS NULL`
3. **敏感数据**: 密码使用bcrypt加密，支付信息加密存储
4. **审计日志**: 重要操作记录操作日志
5. **备份策略**: 每日全量备份，关键时段增量备份

## 11. 错误处理指南

### 11.1 常见约束违反

- **外键约束错误**: 检查关联数据是否存在
- **唯一约束错误**: 检查重复数据（如邮箱、手机号）
- **非空约束错误**: 检查必填字段

### 11.2 数据一致性检查

```sql
-- 检查订单金额一致性
SELECT o.id, o.total_amount, SUM(oi.total_price) as calculated_total
FROM orders o
JOIN order_items oi ON o.id = oi.order_id
GROUP BY o.id, o.total_amount
HAVING o.total_amount != SUM(oi.total_price);

-- 检查客户统计数据一致性
SELECT c.id, c.total_orders, COUNT(o.id) as actual_orders
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
GROUP BY c.id, c.total_orders
HAVING c.total_orders != COUNT(o.id);
```

---

本文档提供了数据库操作的完整指南，建议定期根据业务发展情况更新维护。如有疑问，请联系技术团队。
