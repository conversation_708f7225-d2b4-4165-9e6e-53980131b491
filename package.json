{"name": "snackorder-nestjs", "version": "1.0.0", "description": "NestJS API 服务，为 Web 点餐系统提供多租户支持", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:push": "prisma db push", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.12.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "nest-winston": "^1.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.5", "@types/bcrypt": "^6.0.0", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "globals": "^16.3.0", "jest": "^30.0.5", "prettier": "^3.6.2", "prisma": "^6.12.0", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1", "^@/common/(.*)$": "<rootDir>/common/$1", "^@/config/(.*)$": "<rootDir>/config/$1", "^@prisma/client$": "<rootDir>/../generated/prisma"}, "preset": "ts-jest"}, "packageManager": "pnpm@10.13.1"}