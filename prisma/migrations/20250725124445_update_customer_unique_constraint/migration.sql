/*
  Warnings:

  - A unique constraint covering the columns `[orderItemId]` on the table `order_item_attribute_options` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "order_item_attribute_options_orderItemId_optionId_key";

-- CreateIndex
CREATE INDEX "customers_tenantId_idx" ON "customers"("tenantId");

-- CreateIndex
CREATE INDEX "order_item_attribute_options_optionId_idx" ON "order_item_attribute_options"("optionId");

-- CreateIndex
CREATE UNIQUE INDEX "order_item_attribute_options_orderItemId_key" ON "order_item_attribute_options"("orderItemId");

-- CreateIndex
CREATE INDEX "order_items_productId_idx" ON "order_items"("productId");

-- CreateIndex
CREATE INDEX "orders_handledBy_idx" ON "orders"("handledBy");
