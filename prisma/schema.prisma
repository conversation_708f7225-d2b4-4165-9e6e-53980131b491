// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"

  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

// ================================
// 枚举定义 (Enum Definitions)
// ================================

enum GeneralStatus {
  ACTIVE // 激活
  INACTIVE // 禁用
}

enum TenantStatus {
  ACTIVE // 正常营业
  INACTIVE // 暂停营业
  SUSPENDED // 被平台暂停
}

enum UserRole {
  MANAGER // 店长
  STAFF // 店员
}

enum SmsCodeType {
  LOGIN // 登录验证
  ORDER // 下单验证
  PAYMENT // 支付验证
}

enum ProductAttributeType {
  SIZE // 规格（大中小杯等）
  INGREDIENT // 配料（糖度、温度等）
  COMBO // 组合套餐
  ADDON // 附加选项
}

enum OrderStatus {
  PENDING_PAYMENT // 待支付
  PAID // 已支付
  PROCESSING // 制作中
  COMPLETED // 已完成
  CANCELLED // 已取消
  REFUNDED // 已退款
}

enum PaymentStatus {
  UNPAID // 未支付
  PAID // 已支付
  REFUNDED // 已退款
}

enum PaymentMethod {
  WECHAT // 微信支付
  ALIPAY // 支付宝
  CASH // 现金
  CARD // 银行卡
}

enum PrintJobStatus {
  PENDING // 待打印
  PRINTED // 已打印
  FAILED // 打印失败
}

enum PrintJobType {
  ORDER_RECEIPT // 下单小票
  PAYMENT_RECEIPT // 支付小票
  COMPLETION_RECEIPT // 完成小票
}

enum OrderSource {
  MINIPROGRAM // 小程序
  H5 // 网页
  APP // 手机应用
  POS // 收银台
}

enum CustomerLevel {
  NORMAL // 普通客户
  VIP // VIP客户
  DIAMOND // 钻石客户
  BLACKLIST // 黑名单
}

// ================================
// 数据模型定义 (Data Models)
// ================================

/// 平台管理员表 - 系统管理员账号
model PlatformAdmin {
  id          String        @id @default(cuid()) /// 主键ID
  email       String        @unique /// 邮箱地址（唯一）
  password    String /// 密码hash
  name        String /// 管理员姓名
  status      GeneralStatus @default(ACTIVE) /// 账号状态
  lastLoginAt DateTime? /// 最后登录时间
  createdAt   DateTime      @default(now()) /// 创建时间
  updatedAt   DateTime      @updatedAt /// 更新时间

  @@map("platform_admins")
}

/// 租户表 - 每个商户/店铺的基本信息
model Tenant {
  id          String       @id @default(cuid()) /// 主键ID
  name        String /// 商户名称
  slug        String       @unique /// 路由标识符（唯一）
  description String? /// 商户描述
  logo        String? /// 商户LOGO
  phone       String? /// 商户联系电话
  address     String? /// 商户地址
  status      TenantStatus @default(ACTIVE) /// 商户状态
  deletedAt   DateTime? /// 软删除时间
  createdAt   DateTime     @default(now()) /// 创建时间
  updatedAt   DateTime     @updatedAt /// 更新时间

  // 关联表
  users      TenantUser[] /// 商户用户
  categories Category[] /// 商品分类
  products   Product[] /// 商品列表
  orders     Order[] /// 订单列表
  customers  Customer[] /// 客户列表
  settings   TenantSetting[] /// 商户设置

  @@index([status])
  @@index([deletedAt])
  @@map("tenants")
}

/// 商户设置表 - 存储租户个性化配置
model TenantSetting {
  id        String   @id @default(cuid()) /// 主键ID
  tenantId  String /// 租户ID
  key       String /// 设置键名
  value     String /// 设置值
  createdAt DateTime @default(now()) /// 创建时间
  updatedAt DateTime @updatedAt /// 更新时间

  // 关联
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, key])
  @@index([tenantId])
  @@map("tenant_settings")
}

/// 商户用户表 - 店长和店员账号
model TenantUser {
  id          String        @id @default(cuid())
  tenantId    String /// 租户ID
  email       String        @unique // 租户用户的邮箱，在所有租户中唯一
  name        String? // 用户姓名
  password    String // 哈希后的密码
  role        UserRole /// 用户角色
  status      GeneralStatus @default(ACTIVE) /// 账号状态
  lastLoginAt DateTime? /// 最后登录时间
  deletedAt   DateTime? /// 软删除时间
  createdAt   DateTime      @default(now()) /// 创建时间
  updatedAt   DateTime      @updatedAt /// 更新时间

  // 关联
  tenant Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  orders Order[] /// 处理的订单

  @@index([tenantId])
  @@index([email])
  @@index([deletedAt])
  @@map("tenant_users")
}

/// 顾客表 - 客户信息和CRM数据
model Customer {
  id          String        @id @default(cuid()) /// 主键ID
  tenantId    String /// 租户ID
  phone       String /// 手机号（主要标识）
  name        String? /// 姓名（可选）
  level       CustomerLevel @default(NORMAL) /// 客户等级
  lastOrderAt DateTime? /// 最后下单时间
  totalOrders Int           @default(0) /// 订单总数
  totalAmount Decimal       @default(0) @db.Decimal(10, 2) /// 消费总额
  tags        String[]      @default([]) /// CRM标签
  notes       String? /// 客户备注
  birthday    DateTime? /// 生日
  deletedAt   DateTime? /// 软删除时间
  createdAt   DateTime      @default(now()) /// 创建时间
  updatedAt   DateTime      @updatedAt /// 更新时间

  // 关联
  tenant Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  orders Order[] /// 客户订单

  @@unique([phone, tenantId]) /// 同一租户下手机号唯一
  @@index([tenantId])
  @@index([level])
  @@index([deletedAt])
  @@map("customers")
}

/// 短信验证码表 - 验证码记录
model SmsCode {
  id        String      @id @default(cuid()) /// 主键ID
  phone     String /// 手机号
  code      String /// 验证码
  type      SmsCodeType /// 验证码类型
  used      Boolean     @default(false) /// 是否已使用
  expiresAt DateTime /// 过期时间
  createdAt DateTime    @default(now()) /// 创建时间

  @@index([phone, type, used])
  @@index([expiresAt]) /// 用于清理过期数据
  @@map("sms_codes")
}

/// 商品分类表 - 商品分类管理
model Category {
  id        String        @id @default(cuid()) /// 主键ID
  tenantId  String /// 租户ID
  name      String /// 分类名称
  sort      Int           @default(0) /// 排序权重
  status    GeneralStatus @default(ACTIVE) /// 分类状态
  deletedAt DateTime? /// 软删除时间
  createdAt DateTime      @default(now()) /// 创建时间
  updatedAt DateTime      @updatedAt /// 更新时间

  // 关联
  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  products Product[] /// 分类下的商品

  @@index([tenantId, status])
  @@index([deletedAt])
  @@map("categories")
}

/// 商品表 - 商品基本信息
model Product {
  id          String        @id @default(cuid()) /// 主键ID
  tenantId    String /// 租户ID
  categoryId  String? /// 分类ID（可选）
  name        String /// 商品名称
  description String? /// 商品描述
  price       Decimal       @db.Decimal(10, 2) /// 基础价格
  image       String? /// 商品图片
  stock       Int? /// 库存数量（null表示不限库存）
  sort        Int           @default(0) /// 排序权重
  status      GeneralStatus @default(ACTIVE) /// 商品状态
  deletedAt   DateTime? /// 软删除时间
  createdAt   DateTime      @default(now()) /// 创建时间
  updatedAt   DateTime      @updatedAt /// 更新时间

  // 关联
  tenant     Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  category   Category?          @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  attributes ProductAttribute[] /// 商品属性
  orderItems OrderItem[] /// 订单项记录

  @@index([tenantId, status])
  @@index([categoryId])
  @@index([deletedAt])
  @@map("products")
}

/// 商品属性表 - 规格、配料、组合等属性定义
model ProductAttribute {
  id        String               @id @default(cuid()) /// 主键ID
  productId String /// 商品ID
  type      ProductAttributeType /// 属性类型
  name      String /// 属性名称（如"杯型"、"甜度"）
  required  Boolean              @default(false) /// 是否必选
  multiple  Boolean              @default(false) /// 是否可多选
  sort      Int                  @default(0) /// 排序权重
  createdAt DateTime             @default(now()) /// 创建时间
  updatedAt DateTime             @updatedAt /// 更新时间

  // 关联
  product Product                  @relation(fields: [productId], references: [id], onDelete: Cascade)
  options ProductAttributeOption[] /// 属性选项

  @@index([productId])
  @@map("product_attributes")
}

/// 商品属性选项表 - 属性的具体选项值
model ProductAttributeOption {
  id          String        @id @default(cuid()) /// 主键ID
  attributeId String /// 属性ID
  name        String /// 选项名称（如"大杯"、"无糖"）
  price       Decimal       @default(0) @db.Decimal(10, 2) /// 附加价格
  sort        Int           @default(0) /// 排序权重
  status      GeneralStatus @default(ACTIVE) /// 选项状态
  createdAt   DateTime      @default(now()) /// 创建时间
  updatedAt   DateTime      @updatedAt /// 更新时间

  // 关联
  attribute        ProductAttribute           @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  orderItemOptions OrderItemAttributeOption[] /// 订单项选项记录

  @@index([attributeId])
  @@map("product_attribute_options")
}

/// 订单表 - 客户订单主表
model Order {
  id            String        @id @default(cuid()) /// 主键ID
  tenantId      String /// 租户ID
  customerId    String /// 客户ID
  orderNumber   String        @unique /// 订单号（全局唯一）
  source        OrderSource   @default(MINIPROGRAM) /// 订单来源
  totalAmount   Decimal       @db.Decimal(10, 2) /// 订单总金额
  status        OrderStatus   @default(PENDING_PAYMENT) /// 订单状态
  paymentStatus PaymentStatus @default(UNPAID) /// 支付状态
  note          String? /// 订单备注
  handledBy     String? /// 处理人员ID
  paidAt        DateTime? /// 支付时间
  completedAt   DateTime? /// 完成时间
  createdAt     DateTime      @default(now()) /// 创建时间
  updatedAt     DateTime      @updatedAt /// 更新时间

  // 关联
  tenant   Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  customer Customer    @relation(fields: [customerId], references: [id], onDelete: Restrict)
  handler  TenantUser? @relation(fields: [handledBy], references: [id], onDelete: SetNull)
  items    OrderItem[] /// 订单商品项
  payments Payment[] /// 支付记录

  @@index([tenantId, status])
  @@index([customerId])
  @@index([handledBy])
  @@index([createdAt])
  @@map("orders")
}

/// 订单商品表 - 订单中的具体商品项
model OrderItem {
  id          String   @id @default(cuid()) /// 主键ID
  orderId     String /// 订单ID
  productId   String /// 商品ID
  productName String /// 商品名称快照
  quantity    Int      @default(1) /// 商品数量
  unitPrice   Decimal  @db.Decimal(10, 2) /// 商品单价快照
  totalPrice  Decimal  @db.Decimal(10, 2) /// 小计金额
  createdAt   DateTime @default(now()) /// 创建时间

  // 关联
  order   Order                      @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product                    @relation(fields: [productId], references: [id], onDelete: Restrict)
  options OrderItemAttributeOption[] /// 选择的属性选项

  @@index([orderId])
  @@index([productId])
  @@map("order_items")
}

/// 订单商品属性选项表 - 订单商品的选择选项记录
model OrderItemAttributeOption {
  id          String   @id @default(cuid()) /// 主键ID
  orderItemId String /// 订单商品项ID
  optionId    String /// 属性选项ID
  optionName  String /// 选项名称快照
  price       Decimal  @db.Decimal(10, 2) /// 选项价格快照
  createdAt   DateTime @default(now()) /// 创建时间

  // 关联
  orderItem OrderItem              @relation(fields: [orderItemId], references: [id], onDelete: Cascade)
  option    ProductAttributeOption @relation(fields: [optionId], references: [id], onDelete: Restrict)

  @@unique([orderItemId])
  @@index([orderItemId])
  @@index([optionId])
  @@map("order_item_attribute_options")
}

/// 支付记录表 - 订单支付流水记录
model Payment {
  id           String        @id @default(cuid()) /// 主键ID
  orderId      String /// 订单ID
  amount       Decimal       @db.Decimal(10, 2) /// 支付金额
  method       PaymentMethod /// 支付方式
  status       PaymentStatus @default(UNPAID) /// 支付状态
  thirdPartyId String? /// 第三方支付平台交易ID
  paidAt       DateTime? /// 支付完成时间
  createdAt    DateTime      @default(now()) /// 创建时间
  updatedAt    DateTime      @updatedAt /// 更新时间

  // 关联
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([status])
  @@index([thirdPartyId])
  @@map("payments")
}

/// 打印任务表 - 小票打印队列管理
model PrintJob {
  id         String         @id @default(cuid()) /// 主键ID
  tenantId   String /// 租户ID
  orderId    String /// 订单ID
  type       PrintJobType /// 打印类型
  status     PrintJobStatus @default(PENDING) /// 打印状态
  retryCount Int            @default(0) /// 重试次数
  printedAt  DateTime? /// 打印完成时间
  createdAt  DateTime       @default(now()) /// 创建时间
  updatedAt  DateTime       @updatedAt /// 更新时间

  @@index([tenantId, status])
  @@index([orderId])
  @@map("print_jobs")
}
