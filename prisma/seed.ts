// prisma/seed.ts
import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  const hashed = await bcrypt.hash('A123456!', 10); // 初始密码
  await prisma.platformAdmin.upsert({
    where: { email: '<EMAIL>' },
    update: {},           // 已存在则跳过
    create: {
      email: '<EMAIL>',
      password: hashed,
      name: 'Super Admin',
    },
  });

  console.log('✅ 管理员账号已初始化: <EMAIL> / A123456!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
