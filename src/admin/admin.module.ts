import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PrismaModule } from '@/common/prisma/prisma.module';
import { AdminService } from './services/admin.service';
import { AdminAuthService } from './auth/services/admin-auth.service';
import { AdminLocalStrategy } from './auth/strategies/admin-local.strategy';
import { AdminJwtStrategy } from './auth/strategies/admin-jwt.strategy';
import { AdminAuthController } from './auth/controllers/admin-auth.controller';
import { AdminTenantService } from './tenants/services/admin-tenant.service';
import { AdminTenantController } from './tenants/controllers/admin-tenant.controller';

@Module({
  imports: [
    PrismaModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('ADMIN_JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('ADMIN_JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AdminAuthController, AdminTenantController],
  providers: [
    AdminService,
    AdminAuthService,
    AdminTenantService,
    AdminLocalStrategy,
    AdminJwtStrategy,
  ],
  exports: [AdminService, AdminTenantService],
})
export class AdminModule {}
