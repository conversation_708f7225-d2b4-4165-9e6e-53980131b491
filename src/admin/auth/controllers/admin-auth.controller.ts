import { Controller, Post, UseGuards, Request, Get, Version } from '@nestjs/common';
import { AdminAuthService } from '../services/admin-auth.service';
import { AdminLocalAuthGuard } from '../guards/admin-local-auth.guard';
import { AdminJwtAuthGuard } from '../guards/admin-jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { AdminLoginDto } from '../dto/admin-login.dto';

@ApiTags('管理员认证')
@Controller('admin/auth')
export class AdminAuthController {
  constructor(private readonly adminAuthService: AdminAuthService) {}


  @ApiOperation({ summary: '管理员登录' })
  @ApiBody({ type: AdminLoginDto })
  @UseGuards(AdminLocalAuthGuard)
  @Version('1')
  @Post('login')
  async login(@Request() req) {
    return this.adminAuthService.login(req.user);
  }

  @ApiOperation({ summary: '获取管理员信息' })
  @ApiBearerAuth()
  @UseGuards(AdminJwtAuthGuard)
  @Version('1')
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }
}
