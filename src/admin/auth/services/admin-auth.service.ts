import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AdminService } from '@/admin/services/admin.service';
import { PlatformAdmin } from '@prisma/client';

// Omit a single property from a type
type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export type AdminPayload = Omit<PlatformAdmin, 'password'>;

@Injectable()
export class AdminAuthService {
  constructor(
    private readonly adminService: AdminService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 验证平台管理员凭据
   * @param email 邮箱
   * @param pass 密码
   * @returns 成功则返回管理员信息（不含密码），失败则返回 null
   */
  async validateAdmin(email: string, pass: string): Promise<AdminPayload | null> {
    const admin = await this.adminService.findOneByEmail(email);
    if (admin && (await this.adminService.validatePassword(pass, admin.password))) {
      const { password, ...result } = admin;
      return result;
    }
    return null;
  }

  /**
   * 管理员登录并生成 JWT
   * @param admin 管理员信息
   * @returns 返回包含 access_token 的对象
   */
  async login(admin: AdminPayload): Promise<{ access_token: string }> {
    const payload = {
      email: admin.email,
      sub: admin.id,
      // 在 payload 中明确标识这是管理员
      isAdmin: true,
    };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}
