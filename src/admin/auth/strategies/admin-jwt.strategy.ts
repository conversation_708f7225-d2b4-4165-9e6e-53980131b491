import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AdminService } from '@/admin/services/admin.service';

// 策略的名称 'admin-jwt' 用于区分商户用户的 'jwt' 策略
@Injectable()
export class AdminJwtStrategy extends PassportStrategy(Strategy, 'admin-jwt') {
  constructor(
    private readonly configService: ConfigService,
    private readonly adminService: AdminService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey:
        configService.get<string>('ADMIN_JWT_SECRET') ||
        'admin-jwt-secret-fallback',
    });
  }

  /**
   * 验证 JWT 载荷
   * @param payload 解码后的 JWT 载荷
   * @returns 返回管理员信息，如果用户不存在或token不合法则抛出异常
   */
  async validate(payload: { sub: string; email: string; isAdmin: boolean }) {
    // 验证这是否是一个管理员 token
    if (!payload.isAdmin) {
      throw new UnauthorizedException('无效的管理员凭证');
    }

    const admin = await this.adminService.findOneByEmail(payload.email);

    if (!admin) {
      throw new UnauthorizedException('管理员不存在或已被禁用');
    }

    // 剔除密码，避免敏感信息泄露
    const { password, ...result } = admin;
    return result;
  }
}
