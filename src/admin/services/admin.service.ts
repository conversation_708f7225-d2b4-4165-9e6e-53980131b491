import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { PlatformAdmin } from '@prisma/client';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 根据邮箱查找平台管理员
   * @param email 管理员邮箱
   * @returns 返回管理员信息，或在找不到时返回 null
   */
  async findOneByEmail(email: string): Promise<PlatformAdmin | null> {
    return this.prisma.platformAdmin.findUnique({
      where: { email },
    });
  }

  /**
   * 验证管理员密码
   * @param plainPassword 明文密码
   * @param hashedPassword 哈希后的密码
   * @returns 如果密码匹配则返回 true，否则返回 false
   */
  async validatePassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
