import { Test, TestingModule } from '@nestjs/testing';
import { AdminTenantController } from './admin-tenant.controller';
import { AdminTenantService } from '../services/admin-tenant.service';
import { TenantStatus } from '@prisma/client';
import {
  CreateTenantDto,
  UpdateTenantDto,
  TenantQueryDto,
  InitializeTenantManagerDto,
} from '../dto';

describe('AdminTenantController', () => {
  let controller: AdminTenantController;
  let service: jest.Mocked<AdminTenantService>;

  const mockTenantResponse = {
    id: 'tenant-1',
    name: '测试商户',
    slug: 'test-tenant',
    description: '测试描述',
    logo: 'https://example.com/logo.png',
    phone: '13800138000',
    address: '测试地址',
    status: TenantStatus.ACTIVE,
    createdAt: new Date(),
    updatedAt: new Date(),
    manager: {
      id: 'manager-1',
      email: '<EMAIL>',
      name: '店长',
      lastLoginAt: new Date(),
    },
  };

  const mockService = {
    createTenant: jest.fn(),
    getTenants: jest.fn(),
    getTenantById: jest.fn(),
    updateTenant: jest.fn(),
    deleteTenant: jest.fn(),
    initializeTenantManager: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminTenantController],
      providers: [
        {
          provide: AdminTenantService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<AdminTenantController>(AdminTenantController);
    service = module.get(AdminTenantService);

    jest.clearAllMocks();
  });

  describe('createTenant', () => {
    it('应该创建商户并返回商户信息', async () => {
      // Arrange
      const createTenantDto: CreateTenantDto = {
        name: '新商户',
        slug: 'new-tenant',
        description: '新商户描述',
      };
      service.createTenant.mockResolvedValue(mockTenantResponse);

      // Act
      const result = await controller.createTenant(createTenantDto);

      // Assert
      expect(service.createTenant).toHaveBeenCalledWith(createTenantDto);
      expect(result).toEqual(mockTenantResponse);
    });
  });

  describe('getTenants', () => {
    it('应该返回商户列表', async () => {
      // Arrange
      const queryDto: TenantQueryDto = {
        page: 1,
        limit: 10,
        search: '测试',
        status: TenantStatus.ACTIVE,
      };
      const mockListResponse = {
        data: [mockTenantResponse],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };
      service.getTenants.mockResolvedValue(mockListResponse);

      // Act
      const result = await controller.getTenants(queryDto);

      // Assert
      expect(service.getTenants).toHaveBeenCalledWith(queryDto);
      expect(result).toEqual(mockListResponse);
    });
  });

  describe('getTenantById', () => {
    it('应该返回指定商户信息', async () => {
      // Arrange
      const tenantId = 'tenant-1';
      service.getTenantById.mockResolvedValue(mockTenantResponse);

      // Act
      const result = await controller.getTenantById(tenantId);

      // Assert
      expect(service.getTenantById).toHaveBeenCalledWith(tenantId);
      expect(result).toEqual(mockTenantResponse);
    });
  });

  describe('updateTenant', () => {
    it('应该更新商户并返回更新后的信息', async () => {
      // Arrange
      const tenantId = 'tenant-1';
      const updateDto: UpdateTenantDto = {
        name: '更新的商户名',
      };
      const updatedResponse = { ...mockTenantResponse, name: '更新的商户名' };
      service.updateTenant.mockResolvedValue(updatedResponse);

      // Act
      const result = await controller.updateTenant(tenantId, updateDto);

      // Assert
      expect(service.updateTenant).toHaveBeenCalledWith(tenantId, updateDto);
      expect(result).toEqual(updatedResponse);
    });
  });

  describe('deleteTenant', () => {
    it('应该删除商户', async () => {
      // Arrange
      const tenantId = 'tenant-1';
      service.deleteTenant.mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteTenant(tenantId);

      // Assert
      expect(service.deleteTenant).toHaveBeenCalledWith(tenantId);
      expect(result).toBeUndefined();
    });
  });

  describe('initializeTenantManager', () => {
    it('应该初始化店长账号并返回结果', async () => {
      // Arrange
      const tenantId = 'tenant-1';
      const initManagerDto: InitializeTenantManagerDto = {
        email: '<EMAIL>',
        name: '店长',
      };
      const mockInitResponse = {
        success: true,
        managerId: 'manager-1',
        email: '<EMAIL>',
        initialPassword: 'abcd1234',
        message: '店长账号初始化成功',
      };
      service.initializeTenantManager.mockResolvedValue(mockInitResponse);

      // Act
      const result = await controller.initializeTenantManager(
        tenantId,
        initManagerDto,
      );

      // Assert
      expect(service.initializeTenantManager).toHaveBeenCalledWith(
        tenantId,
        initManagerDto,
      );
      expect(result).toEqual(mockInitResponse);
    });
  });

  describe('healthCheck', () => {
    it('应该返回健康检查信息', async () => {
      // Act
      const result = await controller.healthCheck();

      // Assert
      expect(result.message).toBe('管理员商户服务运行正常');
      expect(result.timestamp).toBeDefined();
    });
  });
});
