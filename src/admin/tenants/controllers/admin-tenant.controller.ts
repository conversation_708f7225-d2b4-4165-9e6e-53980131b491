import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AdminJwtAuthGuard } from '@/admin/auth/guards/admin-jwt-auth.guard';
import { AdminTenantService } from '../services/admin-tenant.service';
import {
  CreateTenantDto,
  UpdateTenantDto,
  TenantQueryDto,
  InitializeTenantManagerDto,
  TenantResponseDto,
  TenantListResponseDto,
  InitializeManagerResponseDto,
} from '../dto';

@ApiTags('管理员 - 商户管理')
@ApiBearerAuth()
@UseGuards(AdminJwtAuthGuard)
@Controller('admin/tenants')
export class AdminTenantController {
  constructor(private readonly adminTenantService: AdminTenantService) {}

  @Post()
  @ApiOperation({ summary: '创建商户' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '商户创建成功',
    type: TenantResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '路由标识符已存在',
  })
  async createTenant(
    @Body() createTenantDto: CreateTenantDto,
  ): Promise<TenantResponseDto> {
    return this.adminTenantService.createTenant(createTenantDto);
  }

  @Get()
  @ApiOperation({ summary: '获取商户列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取商户列表成功',
    type: TenantListResponseDto,
  })
  async getTenants(
    @Query() queryDto: TenantQueryDto,
  ): Promise<TenantListResponseDto> {
    return this.adminTenantService.getTenants(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取商户详情' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取商户详情成功',
    type: TenantResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '商户不存在',
  })
  async getTenantById(@Param('id') id: string): Promise<TenantResponseDto> {
    return this.adminTenantService.getTenantById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新商户信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '商户更新成功',
    type: TenantResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '商户不存在',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '路由标识符已存在',
  })
  async updateTenant(
    @Param('id') id: string,
    @Body() updateTenantDto: UpdateTenantDto,
  ): Promise<TenantResponseDto> {
    return this.adminTenantService.updateTenant(id, updateTenantDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除商户' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '商户删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '商户不存在',
  })
  async deleteTenant(@Param('id') id: string): Promise<void> {
    return this.adminTenantService.deleteTenant(id);
  }

  @Post(':id/initialize-manager')
  @ApiOperation({ summary: '初始化商户店长账号' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '店长账号初始化成功',
    type: InitializeManagerResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '商户不存在',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '该商户已有店长账号或邮箱已被使用',
  })
  async initializeTenantManager(
    @Param('id') tenantId: string,
    @Body() initManagerDto: InitializeTenantManagerDto,
  ): Promise<InitializeManagerResponseDto> {
    return this.adminTenantService.initializeTenantManager(
      tenantId,
      initManagerDto,
    );
  }

  @Get('test/health')
  @ApiOperation({ summary: '健康检查接口' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '服务正常',
  })
  healthCheck(): { message: string; timestamp: string } {
    return {
      message: '管理员商户服务运行正常',
      timestamp: new Date().toISOString(),
    };
  }
}
