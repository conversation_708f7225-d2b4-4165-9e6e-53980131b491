import {
  IsString,
  <PERSON>NotEmpty,
  <PERSON>Optional,
  IsEmail,
  IsEnum,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TenantStatus } from '@prisma/client';

export class CreateTenantDto {
  @ApiProperty({ description: '商户名称' })
  @IsString()
  @IsNotEmpty({ message: '商户名称不能为空' })
  name: string;

  @ApiProperty({ description: '路由标识符，用于生成商户专属链接' })
  @IsString()
  @IsNotEmpty({ message: '路由标识符不能为空' })
  slug: string;

  @ApiPropertyOptional({ description: '商户描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '商户LOGO图片URL' })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiPropertyOptional({ description: '商户联系电话' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: '商户地址' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: '商户状态',
    enum: TenantStatus,
    default: TenantStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(TenantStatus, { message: '商户状态值不正确' })
  status?: TenantStatus = TenantStatus.ACTIVE;
}
