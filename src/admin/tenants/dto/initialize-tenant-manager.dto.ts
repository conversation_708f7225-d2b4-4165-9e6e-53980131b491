import { IsString, <PERSON>NotEmpty, IsE<PERSON>, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InitializeTenantManagerDto {
  @ApiProperty({ description: '店长邮箱，用于登录' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  email: string;

  @ApiProperty({ description: '店长姓名' })
  @IsString()
  @IsNotEmpty({ message: '店长姓名不能为空' })
  name: string;

  @ApiPropertyOptional({ description: '初始密码，如不提供将自动生成' })
  @IsOptional()
  @IsString()
  password?: string;
}
