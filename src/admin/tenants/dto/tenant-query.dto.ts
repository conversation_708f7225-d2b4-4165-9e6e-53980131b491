import { IsOptional, IsString, IsInt, Min, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { TenantStatus } from '@prisma/client';

export class TenantQueryDto {
  @ApiPropertyOptional({ description: '页码，从1开始', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码必须大于0' })
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量必须大于0' })
  limit?: number = 10;

  @ApiPropertyOptional({ description: '搜索关键词，匹配商户名称' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: '商户状态筛选',
    enum: TenantStatus,
  })
  @IsOptional()
  @IsEnum(TenantStatus, { message: '商户状态值不正确' })
  status?: TenantStatus;
}
