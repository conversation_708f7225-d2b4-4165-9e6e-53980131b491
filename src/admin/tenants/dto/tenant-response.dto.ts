import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TenantStatus } from '@prisma/client';

export class TenantResponseDto {
  @ApiProperty({ description: '商户ID' })
  id: string;

  @ApiProperty({ description: '商户名称' })
  name: string;

  @ApiProperty({ description: '路由标识符' })
  slug: string;

  @ApiPropertyOptional({ description: '商户描述' })
  description?: string;

  @ApiPropertyOptional({ description: '商户LOGO' })
  logo?: string;

  @ApiPropertyOptional({ description: '联系电话' })
  phone?: string;

  @ApiPropertyOptional({ description: '商户地址' })
  address?: string;

  @ApiProperty({ description: '商户状态', enum: TenantStatus })
  status: TenantStatus;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: '店长信息' })
  manager?: {
    id: string;
    email: string;
    name: string;
    lastLoginAt?: Date;
  };
}

export class TenantListResponseDto {
  @ApiProperty({ type: [TenantResponseDto], description: '商户列表' })
  data: TenantResponseDto[];

  @ApiProperty({ description: '总记录数' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;
}

export class InitializeManagerResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '店长用户ID' })
  managerId: string;

  @ApiProperty({ description: '店长邮箱' })
  email: string;

  @ApiProperty({ description: '初始密码' })
  initialPassword: string;

  @ApiProperty({ description: '提示信息' })
  message: string;
}
