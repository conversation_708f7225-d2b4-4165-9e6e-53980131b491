import { Test, TestingModule } from '@nestjs/testing';
import {
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { AdminTenantService } from './admin-tenant.service';
import { PrismaService } from '@/common/prisma/prisma.service';
import { TenantStatus, UserRole, GeneralStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AdminTenantService', () => {
  let service: AdminTenantService;
  let prismaService: jest.Mocked<PrismaService>;

  const mockTenantData = {
    id: 'tenant-1',
    name: '测试商户',
    slug: 'test-tenant',
    description: '测试描述',
    logo: 'https://example.com/logo.png',
    phone: '13800138000',
    address: '测试地址',
    status: TenantStatus.ACTIVE,
    deletedAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    users: [
      {
        id: 'user-1',
        email: '<EMAIL>',
        name: '店长',
        lastLoginAt: new Date(),
      },
    ],
  };

  const mockPrismaService = {
    tenant: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    tenantUser: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminTenantService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<AdminTenantService>(AdminTenantService);
    prismaService = module.get<PrismaService>(PrismaService);

    // 重置所有mock
    jest.clearAllMocks();
  });

  describe('createTenant', () => {
    const createTenantDto = {
      name: '新商户',
      slug: 'new-tenant',
      description: '新商户描述',
    };

    it('应该成功创建商户', async () => {
      // Arrange
      (prismaService.tenant.findUnique as jest.Mock).mockResolvedValue(null);
      (prismaService.tenant.create as jest.Mock).mockResolvedValue({
        ...mockTenantData,
        ...createTenantDto,
        users: [],
      });

      // Act
      const result = await service.createTenant(createTenantDto);

      // Assert
      expect(prismaService.tenant.findUnique).toHaveBeenCalledWith({
        where: { slug: createTenantDto.slug },
      });
      expect(prismaService.tenant.create).toHaveBeenCalledWith({
        data: createTenantDto,
        include: {
          users: {
            where: { role: UserRole.MANAGER },
            select: {
              id: true,
              email: true,
              name: true,
              lastLoginAt: true,
            },
          },
        },
      });
      expect(result.name).toBe(createTenantDto.name);
      expect(result.slug).toBe(createTenantDto.slug);
    });

    it('当slug已存在时应该抛出ConflictException', async () => {
      // Arrange
      (prismaService.tenant.findUnique as jest.Mock).mockResolvedValue(
        mockTenantData,
      );

      // Act & Assert
      await expect(service.createTenant(createTenantDto)).rejects.toThrow(
        new ConflictException('路由标识符已存在'),
      );
    });
  });

  describe('getTenants', () => {
    const queryDto = {
      page: 1,
      limit: 10,
      search: '测试',
      status: TenantStatus.ACTIVE,
    };

    it('应该返回商户列表和分页信息', async () => {
      // Arrange
      const mockTenants = [mockTenantData];
      const mockTotal = 1;

      (prismaService.tenant.findMany as jest.Mock).mockResolvedValue(
        mockTenants,
      );
      (prismaService.tenant.count as jest.Mock).mockResolvedValue(mockTotal);

      // Act
      const result = await service.getTenants(queryDto);

      // Assert
      expect(result.data).toHaveLength(1);
      expect(result.total).toBe(mockTotal);
      expect(result.page).toBe(queryDto.page);
      expect(result.limit).toBe(queryDto.limit);
      expect(result.totalPages).toBe(1);
    });
  });

  describe('getTenantById', () => {
    it('应该返回商户信息', async () => {
      // Arrange
      (prismaService.tenant.findFirst as jest.Mock).mockResolvedValue(
        mockTenantData,
      );

      // Act
      const result = await service.getTenantById('tenant-1');

      // Assert
      expect(prismaService.tenant.findFirst).toHaveBeenCalledWith({
        where: { id: 'tenant-1', deletedAt: null },
        include: {
          users: {
            where: { role: UserRole.MANAGER },
            select: {
              id: true,
              email: true,
              name: true,
              lastLoginAt: true,
            },
          },
        },
      });
      expect(result.id).toBe('tenant-1');
    });

    it('当商户不存在时应该抛出NotFoundException', async () => {
      // Arrange
      prismaService.tenant.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getTenantById('non-existent')).rejects.toThrow(
        new NotFoundException('商户不存在'),
      );
    });
  });

  describe('updateTenant', () => {
    const updateDto = {
      name: '更新的商户名',
    };

    it('应该成功更新商户', async () => {
      // Arrange
      (prismaService.tenant.findFirst as jest.Mock).mockResolvedValue(
        mockTenantData,
      );
      (prismaService.tenant.update as jest.Mock).mockResolvedValue({
        ...mockTenantData,
        ...updateDto,
      });

      // Act
      const result = await service.updateTenant('tenant-1', updateDto);

      // Assert
      expect(result.name).toBe(updateDto.name);
    });

    it('当商户不存在时应该抛出NotFoundException', async () => {
      // Arrange
      prismaService.tenant.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateTenant('non-existent', updateDto),
      ).rejects.toThrow(new NotFoundException('商户不存在'));
    });
  });

  describe('deleteTenant', () => {
    it('应该成功软删除商户', async () => {
      // Arrange
      (prismaService.tenant.findFirst as jest.Mock).mockResolvedValue(
        mockTenantData,
      );
      (prismaService.tenant.update as jest.Mock).mockResolvedValue({
        ...mockTenantData,
        deletedAt: new Date(),
      });

      // Act
      await service.deleteTenant('tenant-1');

      // Assert
      expect(prismaService.tenant.update).toHaveBeenCalledWith({
        where: { id: 'tenant-1' },
        data: {
          deletedAt: expect.any(Date),
          status: TenantStatus.SUSPENDED,
        },
      });
    });
  });

  describe('initializeTenantManager', () => {
    const initManagerDto = {
      email: '<EMAIL>',
      name: '店长',
      password: 'password123',
    };

    it('应该成功初始化店长账号', async () => {
      // Arrange
      (prismaService.tenant.findFirst as jest.Mock).mockResolvedValue(
        mockTenantData,
      );
      (prismaService.tenantUser.findFirst as jest.Mock).mockResolvedValue(null);
      (prismaService.tenantUser.findUnique as jest.Mock).mockResolvedValue(
        null,
      );
      (prismaService.tenantUser.create as jest.Mock).mockResolvedValue({
        id: 'manager-1',
        email: initManagerDto.email,
        name: initManagerDto.name,
        role: UserRole.MANAGER,
        tenantId: 'tenant-1',
      });
      mockedBcrypt.hash.mockResolvedValue('hashed-password');

      // Act
      const result = await service.initializeTenantManager(
        'tenant-1',
        initManagerDto,
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.email).toBe(initManagerDto.email);
      expect(result.initialPassword).toBe(initManagerDto.password);
    });

    it('当商户不存在时应该抛出NotFoundException', async () => {
      // Arrange
      prismaService.tenant.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.initializeTenantManager('non-existent', initManagerDto),
      ).rejects.toThrow(new NotFoundException('商户不存在'));
    });

    it('当商户已有店长时应该抛出ConflictException', async () => {
      // Arrange
      (prismaService.tenant.findFirst as jest.Mock).mockResolvedValue(
        mockTenantData,
      );
      (prismaService.tenantUser.findFirst as jest.Mock).mockResolvedValue({
        id: 'existing-manager',
        role: UserRole.MANAGER,
      });

      // Act & Assert
      await expect(
        service.initializeTenantManager('tenant-1', initManagerDto),
      ).rejects.toThrow(new ConflictException('该商户已有店长账号'));
    });

    it('当邮箱已被使用时应该抛出ConflictException', async () => {
      // Arrange
      (prismaService.tenant.findFirst as jest.Mock).mockResolvedValue(
        mockTenantData,
      );
      (prismaService.tenantUser.findFirst as jest.Mock).mockResolvedValue(null);
      (prismaService.tenantUser.findUnique as jest.Mock).mockResolvedValue({
        id: 'existing-user',
        email: initManagerDto.email,
      });

      // Act & Assert
      await expect(
        service.initializeTenantManager('tenant-1', initManagerDto),
      ).rejects.toThrow(new ConflictException('邮箱已被使用'));
    });
  });
});
