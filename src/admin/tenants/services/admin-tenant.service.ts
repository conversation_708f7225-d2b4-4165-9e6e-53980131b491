import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { UserRole, TenantStatus, GeneralStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import {
  CreateTenantDto,
  UpdateTenantDto,
  TenantQueryDto,
  InitializeTenantManagerDto,
  TenantResponseDto,
  TenantListResponseDto,
  InitializeManagerResponseDto,
} from '../dto';

@Injectable()
export class AdminTenantService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 创建新商户
   * @param createTenantDto 商户创建数据
   * @returns 创建的商户信息
   */
  async createTenant(
    createTenantDto: CreateTenantDto,
  ): Promise<TenantResponseDto> {
    // 检查slug是否已存在
    const existingTenant = await this.prisma.tenant.findUnique({
      where: { slug: createTenantDto.slug },
    });

    if (existingTenant) {
      throw new ConflictException('路由标识符已存在');
    }

    const tenant = await this.prisma.tenant.create({
      data: createTenantDto,
      include: {
        users: {
          where: { role: UserRole.MANAGER },
          select: {
            id: true,
            email: true,
            name: true,
            lastLoginAt: true,
          },
        },
      },
    });

    return this.formatTenantResponse(tenant);
  }

  /**
   * 获取商户列表（支持分页和筛选）
   * @param queryDto 查询参数
   * @returns 商户列表和分页信息
   */
  async getTenants(queryDto: TenantQueryDto): Promise<TenantListResponseDto> {
    const { page = 1, limit = 10, search, status } = queryDto;
    const skip = (page - 1) * limit;

    const where: any = {
      deletedAt: null,
    };

    // 添加搜索条件
    if (search) {
      where.name = {
        contains: search,
        mode: 'insensitive',
      };
    }

    // 添加状态筛选
    if (status) {
      where.status = status;
    }

    const [tenants, total] = await Promise.all([
      this.prisma.tenant.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          users: {
            where: { role: UserRole.MANAGER },
            select: {
              id: true,
              email: true,
              name: true,
              lastLoginAt: true,
            },
          },
        },
      }),
      this.prisma.tenant.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: tenants.map((tenant) => this.formatTenantResponse(tenant)),
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 根据ID获取单个商户信息
   * @param id 商户ID
   * @returns 商户信息
   */
  async getTenantById(id: string): Promise<TenantResponseDto> {
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id,
        deletedAt: null,
      },
      include: {
        users: {
          where: { role: UserRole.MANAGER },
          select: {
            id: true,
            email: true,
            name: true,
            lastLoginAt: true,
          },
        },
      },
    });

    if (!tenant) {
      throw new NotFoundException('Tenant not found');
    }

    return this.formatTenantResponse(tenant);
  }

  /**
   * 更新商户信息
   * @param id 商户ID
   * @param updateTenantDto 更新数据
   * @returns 更新后的商户信息
   */
  async updateTenant(
    id: string,
    updateTenantDto: UpdateTenantDto,
  ): Promise<TenantResponseDto> {
    // 检查商户是否存在
    const existingTenant = await this.prisma.tenant.findFirst({
      where: {
        id,
        deletedAt: null,
      },
    });

    if (!existingTenant) {
      throw new NotFoundException('Tenant not found');
    }

    // 如果要更新slug，检查是否重复
    if (updateTenantDto.slug && updateTenantDto.slug !== existingTenant.slug) {
      const slugExists = await this.prisma.tenant.findUnique({
        where: { slug: updateTenantDto.slug },
      });

      if (slugExists) {
        throw new ConflictException('Slug already exists');
      }
    }

    const updatedTenant = await this.prisma.tenant.update({
      where: { id },
      data: updateTenantDto,
      include: {
        users: {
          where: { role: UserRole.MANAGER },
          select: {
            id: true,
            email: true,
            name: true,
            lastLoginAt: true,
          },
        },
      },
    });

    return this.formatTenantResponse(updatedTenant);
  }

  /**
   * 删除商户（软删除）
   * @param id 商户ID
   */
  async deleteTenant(id: string): Promise<void> {
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id,
        deletedAt: null,
      },
    });

    if (!tenant) {
      throw new NotFoundException('Tenant not found');
    }

    await this.prisma.tenant.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        status: TenantStatus.SUSPENDED,
      },
    });
  }

  /**
   * 初始化商户店长账号
   * @param tenantId 商户ID
   * @param initManagerDto 店长初始化数据
   * @returns 初始化结果
   */
  async initializeTenantManager(
    tenantId: string,
    initManagerDto: InitializeTenantManagerDto,
  ): Promise<InitializeManagerResponseDto> {
    // 检查商户是否存在
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id: tenantId,
        deletedAt: null,
      },
    });

    if (!tenant) {
      throw new NotFoundException('Tenant not found');
    }

    // 检查该商户是否已有店长
    const existingManager = await this.prisma.tenantUser.findFirst({
      where: {
        tenantId,
        role: UserRole.MANAGER,
        deletedAt: null,
      },
    });

    if (existingManager) {
      throw new ConflictException('Tenant already has a manager');
    }

    // 检查邮箱是否已被使用
    const existingUser = await this.prisma.tenantUser.findUnique({
      where: { email: initManagerDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already in use');
    }

    // 生成初始密码
    const initialPassword =
      initManagerDto.password || this.generateRandomPassword();
    const hashedPassword = await bcrypt.hash(initialPassword, 10);

    try {
      const manager = await this.prisma.tenantUser.create({
        data: {
          tenantId,
          email: initManagerDto.email,
          name: initManagerDto.name,
          password: hashedPassword,
          role: UserRole.MANAGER,
          status: GeneralStatus.ACTIVE,
        },
      });

      return {
        success: true,
        managerId: manager.id,
        email: manager.email,
        initialPassword,
        message: 'Manager account initialization successful',
      };
    } catch (error) {
      throw new BadRequestException('Manager account creation failed');
    }
  }

  /**
   * 生成随机密码
   * @returns 8位随机密码
   */
  private generateRandomPassword(): string {
    return crypto.randomBytes(4).toString('hex');
  }

  /**
   * 格式化商户响应数据
   * @param tenant 商户数据
   * @returns 格式化后的响应数据
   */
  private formatTenantResponse(tenant: any): TenantResponseDto {
    const manager = tenant.users?.[0];

    return {
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      description: tenant.description,
      logo: tenant.logo,
      phone: tenant.phone,
      address: tenant.address,
      status: tenant.status,
      createdAt: tenant.createdAt,
      updatedAt: tenant.updatedAt,
      manager: manager
        ? {
            id: manager.id,
            email: manager.email,
            name: manager.name,
            lastLoginAt: manager.lastLoginAt,
          }
        : undefined,
    };
  }
}
