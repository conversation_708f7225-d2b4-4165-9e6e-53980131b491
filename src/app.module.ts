import { <PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from './common/prisma/prisma.module';
import { CoreModule } from './common/core.module';
import { AdminModule } from './admin/admin.module';
import { TenantModule } from './tenant/tenant.module';
import { CustomerModule } from './customer/customer.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // 核心基础设施模块
    CoreModule,

    // 数据库模块
    PrismaModule,

    // 核心业务模块
    AdminModule,
    TenantModule,
    CustomerModule,
  ],
  controllers: [],
  providers: [Logger], // 提供Logger服务
})
export class AppModule {}
