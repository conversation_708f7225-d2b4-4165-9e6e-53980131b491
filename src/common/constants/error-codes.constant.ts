/**
 * 错误代码常量
 * 统一管理所有API错误代码和消息
 */

// 通用错误代码
export const COMMON_ERROR_CODES = {
  // 系统错误 (1000-1999)
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // 请求错误 (2000-2999)
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_REQUEST_FORMAT: 'INVALID_REQUEST_FORMAT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  
  // 认证授权错误 (3000-3999)
  AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // 资源错误 (4000-4999)
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  RESOURCE_LOCKED: 'RESOURCE_LOCKED',
} as const;

// 业务错误代码
export const BUSINESS_ERROR_CODES = {
  // 租户相关错误 (5000-5999)
  TENANT_NOT_FOUND: 'TENANT_NOT_FOUND',
  TENANT_INACTIVE: 'TENANT_INACTIVE',
  TENANT_SUSPENDED: 'TENANT_SUSPENDED',
  TENANT_SLUG_EXISTS: 'TENANT_SLUG_EXISTS',
  
  // 用户相关错误 (6000-6999)
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  USER_INACTIVE: 'USER_INACTIVE',
  INVALID_USER_ROLE: 'INVALID_USER_ROLE',
  
  // 商品相关错误 (7000-7999)
  PRODUCT_NOT_FOUND: 'PRODUCT_NOT_FOUND',
  PRODUCT_INACTIVE: 'PRODUCT_INACTIVE',
  PRODUCT_OUT_OF_STOCK: 'PRODUCT_OUT_OF_STOCK',
  INVALID_PRODUCT_ATTRIBUTE: 'INVALID_PRODUCT_ATTRIBUTE',
  
  // 订单相关错误 (8000-8999)
  ORDER_NOT_FOUND: 'ORDER_NOT_FOUND',
  ORDER_ALREADY_PAID: 'ORDER_ALREADY_PAID',
  ORDER_ALREADY_CANCELLED: 'ORDER_ALREADY_CANCELLED',
  ORDER_CANNOT_BE_MODIFIED: 'ORDER_CANNOT_BE_MODIFIED',
  INVALID_ORDER_STATUS: 'INVALID_ORDER_STATUS',
  
  // 支付相关错误 (9000-9999)
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_ALREADY_PROCESSED: 'PAYMENT_ALREADY_PROCESSED',
  INVALID_PAYMENT_METHOD: 'INVALID_PAYMENT_METHOD',
  PAYMENT_AMOUNT_MISMATCH: 'PAYMENT_AMOUNT_MISMATCH',
} as const;

// 数据库错误代码
export const DATABASE_ERROR_CODES = {
  CONNECTION_ERROR: 'DATABASE_CONNECTION_ERROR',
  CONSTRAINT_VIOLATION: 'DATABASE_CONSTRAINT_VIOLATION',
  FOREIGN_KEY_VIOLATION: 'DATABASE_FOREIGN_KEY_VIOLATION',
  UNIQUE_CONSTRAINT_VIOLATION: 'DATABASE_UNIQUE_CONSTRAINT_VIOLATION',
  TRANSACTION_FAILED: 'DATABASE_TRANSACTION_FAILED',
} as const;

// 合并所有错误代码
export const ERROR_CODES = {
  ...COMMON_ERROR_CODES,
  ...BUSINESS_ERROR_CODES,
  ...DATABASE_ERROR_CODES,
} as const;

// 错误代码类型
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

/**
 * 错误消息映射
 */
export const ERROR_MESSAGES: Record<ErrorCode, string> = {
  // 通用错误消息
  [ERROR_CODES.INTERNAL_SERVER_ERROR]: 'Internal server error occurred',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: 'Service is temporarily unavailable',
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Rate limit exceeded, please try again later',
  
  [ERROR_CODES.VALIDATION_ERROR]: 'Validation failed',
  [ERROR_CODES.INVALID_REQUEST_FORMAT]: 'Invalid request format',
  [ERROR_CODES.MISSING_REQUIRED_FIELD]: 'Required field is missing',
  [ERROR_CODES.INVALID_PARAMETER]: 'Invalid parameter provided',
  
  [ERROR_CODES.AUTHENTICATION_FAILED]: 'Authentication failed',
  [ERROR_CODES.INVALID_TOKEN]: 'Invalid or malformed token',
  [ERROR_CODES.TOKEN_EXPIRED]: 'Token has expired',
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions to perform this action',
  [ERROR_CODES.INVALID_CREDENTIALS]: 'Invalid username or password',
  
  [ERROR_CODES.RESOURCE_NOT_FOUND]: 'Requested resource not found',
  [ERROR_CODES.RESOURCE_ALREADY_EXISTS]: 'Resource already exists',
  [ERROR_CODES.RESOURCE_CONFLICT]: 'Resource conflict detected',
  [ERROR_CODES.RESOURCE_LOCKED]: 'Resource is currently locked',
  
  // 业务错误消息
  [ERROR_CODES.TENANT_NOT_FOUND]: 'Tenant not found',
  [ERROR_CODES.TENANT_INACTIVE]: 'Tenant is inactive',
  [ERROR_CODES.TENANT_SUSPENDED]: 'Tenant has been suspended',
  [ERROR_CODES.TENANT_SLUG_EXISTS]: 'Tenant slug already exists',
  
  [ERROR_CODES.USER_NOT_FOUND]: 'User not found',
  [ERROR_CODES.USER_ALREADY_EXISTS]: 'User already exists',
  [ERROR_CODES.USER_INACTIVE]: 'User account is inactive',
  [ERROR_CODES.INVALID_USER_ROLE]: 'Invalid user role',
  
  [ERROR_CODES.PRODUCT_NOT_FOUND]: 'Product not found',
  [ERROR_CODES.PRODUCT_INACTIVE]: 'Product is inactive',
  [ERROR_CODES.PRODUCT_OUT_OF_STOCK]: 'Product is out of stock',
  [ERROR_CODES.INVALID_PRODUCT_ATTRIBUTE]: 'Invalid product attribute',
  
  [ERROR_CODES.ORDER_NOT_FOUND]: 'Order not found',
  [ERROR_CODES.ORDER_ALREADY_PAID]: 'Order has already been paid',
  [ERROR_CODES.ORDER_ALREADY_CANCELLED]: 'Order has already been cancelled',
  [ERROR_CODES.ORDER_CANNOT_BE_MODIFIED]: 'Order cannot be modified in current status',
  [ERROR_CODES.INVALID_ORDER_STATUS]: 'Invalid order status',
  
  [ERROR_CODES.PAYMENT_FAILED]: 'Payment processing failed',
  [ERROR_CODES.PAYMENT_ALREADY_PROCESSED]: 'Payment has already been processed',
  [ERROR_CODES.INVALID_PAYMENT_METHOD]: 'Invalid payment method',
  [ERROR_CODES.PAYMENT_AMOUNT_MISMATCH]: 'Payment amount does not match order total',
  
  // 数据库错误消息
  [ERROR_CODES.CONNECTION_ERROR]: 'Database connection error',
  [ERROR_CODES.CONSTRAINT_VIOLATION]: 'Database constraint violation',
  [ERROR_CODES.FOREIGN_KEY_VIOLATION]: 'Foreign key constraint violation',
  [ERROR_CODES.UNIQUE_CONSTRAINT_VIOLATION]: 'Unique constraint violation',
  [ERROR_CODES.TRANSACTION_FAILED]: 'Database transaction failed',
};

/**
 * 根据错误代码获取错误消息
 */
export function getErrorMessage(code: ErrorCode): string {
  return ERROR_MESSAGES[code] || ERROR_MESSAGES[ERROR_CODES.INTERNAL_SERVER_ERROR];
}

/**
 * Prisma 错误代码映射
 */
export const PRISMA_ERROR_CODE_MAP: Record<string, ErrorCode> = {
  P2002: ERROR_CODES.UNIQUE_CONSTRAINT_VIOLATION,
  P2003: ERROR_CODES.FOREIGN_KEY_VIOLATION,
  P2025: ERROR_CODES.RESOURCE_NOT_FOUND,
  P2014: ERROR_CODES.CONSTRAINT_VIOLATION,
};
