import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  ResourceNotFoundException,
  BusinessRuleViolationException,
  InvalidCredentialsException,
  TenantNotFoundException,
} from '../exceptions/business.exceptions';
import { RateLimit } from '../guards/rate-limit.guard';

/**
 * 健康检查和测试控制器
 * 用于验证系统基础设施是否正常工作
 */
@ApiTags('系统健康检查')
@Controller('health')
export class HealthController {
  @ApiOperation({ summary: '系统健康检查' })
  @ApiResponse({ status: 200, description: '系统正常运行' })
  @Get()
  healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
    };
  }

  @ApiOperation({ summary: '测试成功响应格式' })
  @ApiResponse({ status: 200, description: '测试响应拦截器' })
  @Get('test-success')
  testSuccessResponse() {
    return {
      message: 'This is a test success response',
      data: {
        test: true,
        timestamp: new Date().toISOString(),
      },
    };
  }

  @ApiOperation({ summary: '测试异常处理' })
  @ApiResponse({ status: 404, description: '测试异常过滤器' })
  @Get('test-error')
  testErrorResponse() {
    throw new ResourceNotFoundException('TestResource', '123');
  }

  @ApiOperation({ summary: '测试业务异常' })
  @ApiResponse({ status: 400, description: '测试业务规则异常' })
  @Get('test-business-error')
  testBusinessError() {
    throw new BusinessRuleViolationException('Test business rule violation', [
      {
        field: 'testField',
        message: 'Test field validation failed',
        value: 'invalidValue',
      },
    ]);
  }

  @ApiOperation({ summary: '测试认证异常' })
  @ApiResponse({ status: 401, description: '测试认证失败' })
  @Get('test-auth-error')
  testAuthError() {
    throw new InvalidCredentialsException();
  }

  @ApiOperation({ summary: '测试租户异常' })
  @ApiResponse({ status: 404, description: '测试租户不存在' })
  @Get('test-tenant-error')
  testTenantError() {
    throw new TenantNotFoundException('tenant_123');
  }

  @ApiOperation({ summary: '测试新响应格式' })
  @ApiResponse({ status: 200, description: '测试新的标准响应格式' })
  @Get('test-new-format')
  testNewFormat() {
    return {
      user: {
        id: 'user_123',
        name: 'Test User',
        email: '<EMAIL>',
      },
      preferences: {
        language: 'en-US',
        theme: 'light',
      },
    };
  }

  @ApiOperation({ summary: '测试HTTP异常' })
  @ApiResponse({ status: 500, description: '测试HTTP异常' })
  @Get('test-http-error')
  testHttpError() {
    throw new HttpException(
      'Test HTTP exception',
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }

  @ApiOperation({ summary: '测试普通错误' })
  @ApiResponse({ status: 500, description: '测试普通JavaScript错误' })
  @Get('test-js-error')
  testJsError() {
    throw new Error('Test JavaScript error');
  }

  @ApiOperation({ summary: '测试限流功能' })
  @ApiResponse({ status: 200, description: '测试限流守卫' })
  @ApiResponse({ status: 429, description: '请求过于频繁' })
  @RateLimit({
    windowMs: 1 * 1000, // 1秒
    max: 1, // 最多1次请求
    message: 'Too many test requests, please try again later',
  })
  @Get('test-rate-limit')
  testRateLimit() {
    return {
      message: 'Rate limit test successful',
      timestamp: new Date().toISOString(),
    };
  }

  @ApiOperation({ summary: '测试POST请求响应格式' })
  @ApiResponse({ status: 201, description: '测试POST请求的响应格式' })
  @Post('test-post')
  testPostResponse(@Body() body: any) {
    return {
      message: 'POST request processed successfully',
      receivedData: body,
      timestamp: new Date().toISOString(),
    };
  }
}
