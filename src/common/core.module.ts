import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { ResponseInterceptor } from './interceptors/response.interceptor';
import {
  RequestLoggerMiddleware,
  RequestContextMiddleware,
} from './middleware/request-logger.middleware';
import { LanguageMiddleware } from './middleware/language.middleware';
import { RateLimitGuard } from './guards/rate-limit.guard';
import { HealthController } from './controllers/health.controller';

/**
 * 核心模块
 * 提供全局的过滤器、拦截器、中间件和守卫
 */
@Module({
  controllers: [HealthController],
  providers: [
    // 全局异常过滤器
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    // 全局响应拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    // 全局限流守卫
    {
      provide: APP_GUARD,
      useClass: RateLimitGuard,
    },
  ],
})
export class CoreModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LanguageMiddleware, RequestContextMiddleware, RequestLoggerMiddleware)
      .forRoutes('*'); // 应用到所有路由
  }
}
