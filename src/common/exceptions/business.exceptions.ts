import { HttpException, HttpStatus } from '@nestjs/common';
import { ERROR_CODES, ErrorCode, getErrorMessage } from '../constants/error-codes.constant';
import { ErrorDetail } from '../interceptors/response.interceptor';

/**
 * 业务异常基类
 */
export abstract class BusinessException extends HttpException {
  constructor(
    errorCode: ErrorCode,
    statusCode: HttpStatus,
    message?: string,
    public readonly details?: ErrorDetail[],
  ) {
    const errorMessage = message || getErrorMessage(errorCode);

    super(
      {
        success: false,
        error: errorCode,
        message: errorMessage,
        details,
      },
      statusCode,
    );
  }
}

/**
 * 资源不存在异常
 */
export class ResourceNotFoundException extends BusinessException {
  constructor(resource: string, identifier?: string | number) {
    const message = identifier
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;

    const details: ErrorDetail[] = [{
      field: 'resource',
      message,
      value: { resource, identifier },
    }];

    super(ERROR_CODES.RESOURCE_NOT_FOUND, HttpStatus.NOT_FOUND, message, details);
  }
}

/**
 * 资源已存在异常
 */
export class ResourceAlreadyExistsException extends BusinessException {
  constructor(resource: string, field: string, value: string) {
    const message = `${resource} with ${field} '${value}' already exists`;
    const details: ErrorDetail[] = [{
      field,
      message,
      value,
    }];

    super(ERROR_CODES.RESOURCE_ALREADY_EXISTS, HttpStatus.CONFLICT, message, details);
  }
}

/**
 * 权限不足异常
 */
export class InsufficientPermissionException extends BusinessException {
  constructor(action: string, resource?: string) {
    const message = resource
      ? `Insufficient permission to ${action} ${resource}`
      : `Insufficient permission to ${action}`;

    const details: ErrorDetail[] = [{
      field: 'permission',
      message,
      value: { action, resource },
    }];

    super(ERROR_CODES.INSUFFICIENT_PERMISSIONS, HttpStatus.FORBIDDEN, message, details);
  }
}

/**
 * 业务规则违反异常
 */
export class BusinessRuleViolationException extends BusinessException {
  constructor(rule: string, details?: ErrorDetail[]) {
    const message = `Business rule violation: ${rule}`;
    super(ERROR_CODES.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, message, details);
  }
}

/**
 * 租户不存在异常
 */
export class TenantNotFoundException extends BusinessException {
  constructor(tenantId: string) {
    const message = `Tenant with ID '${tenantId}' not found`;
    const details: ErrorDetail[] = [{
      field: 'tenantId',
      message,
      value: tenantId,
    }];

    super(ERROR_CODES.TENANT_NOT_FOUND, HttpStatus.NOT_FOUND, message, details);
  }
}

/**
 * 租户状态异常
 */
export class TenantStatusException extends BusinessException {
  constructor(tenantId: string, currentStatus: string, requiredStatus: string) {
    const message = `Tenant '${tenantId}' status is '${currentStatus}', but '${requiredStatus}' is required`;
    const details: ErrorDetail[] = [{
      field: 'tenantStatus',
      message,
      value: { tenantId, currentStatus, requiredStatus },
    }];

    super(ERROR_CODES.TENANT_INACTIVE, HttpStatus.BAD_REQUEST, message, details);
  }
}

/**
 * 认证相关异常
 */
export class AuthenticationException extends BusinessException {
  constructor(message?: string, details?: ErrorDetail[]) {
    super(ERROR_CODES.AUTHENTICATION_FAILED, HttpStatus.UNAUTHORIZED, message, details);
  }
}

/**
 * 无效凭据异常
 */
export class InvalidCredentialsException extends BusinessException {
  constructor() {
    super(ERROR_CODES.INVALID_CREDENTIALS, HttpStatus.UNAUTHORIZED);
  }
}

/**
 * Token 过期异常
 */
export class TokenExpiredException extends AuthenticationException {
  constructor() {
    super('Token has expired');
  }
}

/**
 * 验证码相关异常
 */
export class VerificationCodeException extends BusinessException {
  constructor(message: string, details?: any) {
    super(message, HttpStatus.BAD_REQUEST, 'VERIFICATION_CODE_ERROR', details);
  }
}

/**
 * 验证码无效异常
 */
export class InvalidVerificationCodeException extends VerificationCodeException {
  constructor() {
    super('Invalid or expired verification code');
  }
}

/**
 * 验证码发送频率限制异常
 */
export class VerificationCodeRateLimitException extends VerificationCodeException {
  constructor(waitTime: number) {
    super(
      `Please wait ${waitTime} seconds before requesting another verification code`,
      { waitTime },
    );
  }
}

/**
 * 库存相关异常
 */
export class InventoryException extends BusinessException {
  constructor(message: string, details?: any) {
    super(message, HttpStatus.BAD_REQUEST, 'INVENTORY_ERROR', details);
  }
}

/**
 * 库存不足异常
 */
export class InsufficientStockException extends InventoryException {
  constructor(productId: string, requested: number, available: number) {
    super(
      `Insufficient stock for product '${productId}'. Requested: ${requested}, Available: ${available}`,
      {
        productId,
        requested,
        available,
      },
    );
  }
}

/**
 * 订单相关异常
 */
export class OrderException extends BusinessException {
  constructor(message: string, details?: any) {
    super(message, HttpStatus.BAD_REQUEST, 'ORDER_ERROR', details);
  }
}

/**
 * 订单状态异常
 */
export class InvalidOrderStatusException extends OrderException {
  constructor(orderId: string, currentStatus: string, targetStatus: string) {
    super(
      `Cannot change order '${orderId}' status from '${currentStatus}' to '${targetStatus}'`,
      {
        orderId,
        currentStatus,
        targetStatus,
      },
    );
  }
}

/**
 * 支付相关异常
 */
export class PaymentException extends BusinessException {
  constructor(message: string, details?: any) {
    super(message, HttpStatus.BAD_REQUEST, 'PAYMENT_ERROR', details);
  }
}

/**
 * 支付失败异常
 */
export class PaymentFailedException extends PaymentException {
  constructor(reason: string, details?: any) {
    super(`Payment failed: ${reason}`, details);
  }
}
