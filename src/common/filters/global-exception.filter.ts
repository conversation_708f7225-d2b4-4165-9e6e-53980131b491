import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import {
  ApiErrorResponse,
  ErrorDetail
} from '../interceptors/response.interceptor';
import {
  ERROR_CODES,
  ErrorCode,
  getErrorMessage,
  PRISMA_ERROR_CODE_MAP
} from '../constants/error-codes.constant';

/**
 * 全局异常过滤器
 * 统一处理所有未捕获的异常，提供标准化的错误响应格式
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const { statusCode, errorResponse } = this.buildErrorResponse(exception, request);

    // 记录错误日志
    this.logError(exception, request, statusCode, errorResponse);

    response.status(statusCode).json(errorResponse);
  }

  /**
   * 构建标准化的错误响应
   */
  private buildErrorResponse(exception: unknown, request: Request): {
    statusCode: number;
    errorResponse: ApiErrorResponse;
  } {
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorCode: ErrorCode = ERROR_CODES.INTERNAL_SERVER_ERROR;
    let message = getErrorMessage(ERROR_CODES.INTERNAL_SERVER_ERROR);
    let details: ErrorDetail[] = [];

    if (exception instanceof HttpException) {
      // NestJS HTTP 异常
      statusCode = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
        errorCode = this.mapHttpStatusToErrorCode(statusCode);
      } else if (typeof exceptionResponse === 'object') {
        const response = exceptionResponse as any;

        // 如果是我们自定义的业务异常，直接使用其错误代码
        if (response.error && Object.values(ERROR_CODES).includes(response.error)) {
          errorCode = response.error;
          message = response.message || getErrorMessage(errorCode);
          details = response.details || [];
        } else {
          // 处理验证错误
          if (Array.isArray(response.message)) {
            errorCode = ERROR_CODES.VALIDATION_ERROR;
            message = getErrorMessage(ERROR_CODES.VALIDATION_ERROR);
            details = response.message.map((msg: string) => ({
              message: msg,
            }));
          } else {
            message = response.message || response.error || exception.message;
            errorCode = this.mapHttpStatusToErrorCode(statusCode);
          }
        }
      }
    } else if (exception instanceof PrismaClientKnownRequestError) {
      // Prisma 数据库错误
      const prismaError = this.handlePrismaError(exception);
      statusCode = prismaError.statusCode;
      errorCode = prismaError.errorCode;
      message = prismaError.message;
      details = prismaError.details || [];
    } else if (exception instanceof Error) {
      // 普通 JavaScript 错误
      message = exception.message || getErrorMessage(ERROR_CODES.INTERNAL_SERVER_ERROR);
      errorCode = ERROR_CODES.INTERNAL_SERVER_ERROR;
    }

    const errorResponse: ApiErrorResponse = {
      success: false,
      error: {
        code: errorCode,
        message,
        details: details.length > 0 ? details : undefined,
      },
      timestamp: new Date().toISOString(),
    };

    // 在开发环境中添加调试信息
    if (process.env.NODE_ENV === 'development') {
      (errorResponse as any).debug = {
        path: request.url,
        method: request.method,
        stack: exception instanceof Error ? exception.stack : undefined,
      };
    }

    return { statusCode, errorResponse };
  }

  /**
   * 处理 Prisma 数据库错误
   */
  private handlePrismaError(error: PrismaClientKnownRequestError): {
    statusCode: number;
    errorCode: ErrorCode;
    message: string;
    details?: ErrorDetail[];
  } {
    const errorCode = PRISMA_ERROR_CODE_MAP[error.code] || ERROR_CODES.INTERNAL_SERVER_ERROR;
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = getErrorMessage(errorCode);
    let details: ErrorDetail[] = [];

    switch (error.code) {
      case 'P2002':
        // 唯一约束违反
        statusCode = HttpStatus.CONFLICT;
        const target = error.meta?.target as string[];
        const field = target?.[0] || 'field';
        details = [{
          field,
          message: `${field} already exists`,
          value: error.meta?.target,
        }];
        break;

      case 'P2025':
        // 记录不存在
        statusCode = HttpStatus.NOT_FOUND;
        break;

      case 'P2003':
        // 外键约束违反
        statusCode = HttpStatus.BAD_REQUEST;
        details = [{
          field: error.meta?.field_name as string,
          message: 'Referenced record does not exist',
        }];
        break;

      case 'P2014':
        // 关联记录不存在
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      default:
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        break;
    }

    return {
      statusCode,
      errorCode,
      message,
      details: details.length > 0 ? details : undefined,
    };
  }

  /**
   * 将 HTTP 状态码映射到错误代码
   */
  private mapHttpStatusToErrorCode(statusCode: number): ErrorCode {
    switch (statusCode) {
      case HttpStatus.BAD_REQUEST:
        return ERROR_CODES.VALIDATION_ERROR;
      case HttpStatus.UNAUTHORIZED:
        return ERROR_CODES.AUTHENTICATION_FAILED;
      case HttpStatus.FORBIDDEN:
        return ERROR_CODES.INSUFFICIENT_PERMISSIONS;
      case HttpStatus.NOT_FOUND:
        return ERROR_CODES.RESOURCE_NOT_FOUND;
      case HttpStatus.CONFLICT:
        return ERROR_CODES.RESOURCE_CONFLICT;
      case HttpStatus.TOO_MANY_REQUESTS:
        return ERROR_CODES.RATE_LIMIT_EXCEEDED;
      case HttpStatus.SERVICE_UNAVAILABLE:
        return ERROR_CODES.SERVICE_UNAVAILABLE;
      default:
        return ERROR_CODES.INTERNAL_SERVER_ERROR;
    }
  }

  /**
   * 记录错误日志
   */
  private logError(
    exception: unknown,
    request: Request,
    statusCode: number,
    errorResponse: ApiErrorResponse,
  ): void {
    const { method, url, ip, headers } = request;
    const { error } = errorResponse;

    // 构建日志上下文
    const logContext = {
      statusCode,
      method,
      url,
      ip,
      userAgent: headers['user-agent'],
      errorCode: error.code,
      timestamp: new Date().toISOString(),
    };

    // 根据错误级别记录不同的日志
    if (statusCode >= 500) {
      // 服务器错误 - 记录完整的错误信息
      this.logger.error(
        `${method} ${url} - ${error.code}: ${error.message}`,
        exception instanceof Error ? exception.stack : exception,
        JSON.stringify(logContext),
      );
    } else if (statusCode >= 400) {
      // 客户端错误 - 记录警告级别
      this.logger.warn(
        `${method} ${url} - ${error.code}: ${error.message}`,
        JSON.stringify(logContext),
      );
    }
  }
}
