import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

/**
 * 限流配置接口
 */
export interface RateLimitConfig {
  windowMs: number; // 时间窗口（毫秒）
  max: number; // 最大请求次数
  message?: string; // 自定义错误消息
  skipSuccessfulRequests?: boolean; // 是否跳过成功请求
  skipFailedRequests?: boolean; // 是否跳过失败请求
}

/**
 * 限流装饰器
 */
export const RateLimit = (config: RateLimitConfig) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      // 方法装饰器
      Reflect.defineMetadata('rate-limit', config, descriptor.value);
    } else {
      // 类装饰器
      Reflect.defineMetadata('rate-limit', config, target);
    }
  };
};

/**
 * 请求记录接口
 */
interface RequestRecord {
  count: number;
  resetTime: number;
}

/**
 * API 限流守卫
 * 基于 IP 地址和用户的请求频率限制
 */
@Injectable()
export class RateLimitGuard implements CanActivate {
  private readonly logger = new Logger(RateLimitGuard.name);
  private readonly store = new Map<string, RequestRecord>();
  
  // 默认限流配置
  private readonly defaultConfig: RateLimitConfig = {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 100次请求
    message: 'Too many requests, please try again later',
  };

  constructor(private readonly reflector: Reflector) {
    // 定期清理过期记录
    setInterval(() => this.cleanupExpiredRecords(), 60 * 1000); // 每分钟清理一次
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // 获取限流配置
    const config = this.getRateLimitConfig(context);
    if (!config) {
      return true; // 没有配置限流，直接通过
    }

    // 生成限流键
    const key = this.generateKey(request);
    
    // 检查限流
    const isAllowed = this.checkRateLimit(key, config);
    
    if (!isAllowed) {
      this.logger.warn(`Rate limit exceeded for ${key}`, {
        ip: this.getClientIp(request),
        url: request.url,
        method: request.method,
        userAgent: request.headers['user-agent'],
      });
      
      throw new HttpException(
        {
          success: false,
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          error: 'TooManyRequests',
          message: config.message || this.defaultConfig.message,
          retryAfter: Math.ceil(config.windowMs / 1000),
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    return true;
  }

  /**
   * 获取限流配置
   */
  private getRateLimitConfig(context: ExecutionContext): RateLimitConfig | null {
    // 先检查方法级别的配置
    const methodConfig = this.reflector.get<RateLimitConfig>(
      'rate-limit',
      context.getHandler(),
    );
    
    if (methodConfig) {
      return { ...this.defaultConfig, ...methodConfig };
    }

    // 再检查类级别的配置
    const classConfig = this.reflector.get<RateLimitConfig>(
      'rate-limit',
      context.getClass(),
    );
    
    if (classConfig) {
      return { ...this.defaultConfig, ...classConfig };
    }

    // 对于认证相关的端点，使用更严格的限流
    const request = context.switchToHttp().getRequest<Request>();
    if (this.isAuthEndpoint(request.url)) {
      return {
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 5, // 5次尝试
        message: 'Too many authentication attempts, please try again later',
      };
    }

    return null;
  }

  /**
   * 生成限流键
   */
  private generateKey(request: Request): string {
    const ip = this.getClientIp(request);
    const user = (request as any).user?.id || 'anonymous';
    const endpoint = `${request.method}:${request.route?.path || request.url}`;
    
    return `${ip}:${user}:${endpoint}`;
  }

  /**
   * 检查限流
   */
  private checkRateLimit(key: string, config: RateLimitConfig): boolean {
    const now = Date.now();
    const record = this.store.get(key);

    if (!record || now > record.resetTime) {
      // 新记录或已过期，重置计数
      this.store.set(key, {
        count: 1,
        resetTime: now + config.windowMs,
      });
      return true;
    }

    if (record.count >= config.max) {
      return false; // 超过限制
    }

    // 增加计数
    record.count++;
    this.store.set(key, record);
    return true;
  }

  /**
   * 获取客户端 IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      request.ip ||
      'unknown'
    );
  }

  /**
   * 判断是否为认证端点
   */
  private isAuthEndpoint(url: string): boolean {
    const authPatterns = [
      '/auth/login',
      '/auth/register',
      '/auth/forgot-password',
      '/auth/reset-password',
    ];
    
    return authPatterns.some(pattern => url.includes(pattern));
  }

  /**
   * 清理过期记录
   */
  private cleanupExpiredRecords(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, record] of this.store.entries()) {
      if (now > record.resetTime) {
        this.store.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} expired rate limit records`);
    }
  }
}
