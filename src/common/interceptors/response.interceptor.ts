import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';

/**
 * 标准化 API 成功响应格式
 */
export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  message: string;
  code: number;
  timestamp: string;
}

/**
 * 错误详情接口
 */
export interface ErrorDetail {
  field?: string;
  message: string;
  value?: any;
}

/**
 * 标准化 API 错误响应格式
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: ErrorDetail[];
  };
  timestamp: string;
}

/**
 * 兼容旧版本的响应接口（逐步废弃）
 * @deprecated 请使用 ApiSuccessResponse
 */
export interface ApiResponse<T = any> {
  success: boolean;
  statusCode: number;
  message: string;
  data?: T;
  timestamp: string;
  path: string;
  method: string;
}

/**
 * 响应拦截器
 * 统一包装所有成功的 API 响应格式
 */
@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ApiSuccessResponse<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ApiSuccessResponse<T>> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse();

    return next.handle().pipe(
      map((data) => {
        // 如果响应已经是标准格式，直接返回
        if (data && typeof data === 'object' && 'success' in data) {
          return data;
        }

        // 包装成新的标准响应格式
        return {
          success: true,
          data,
          message: this.getSuccessMessage(request.method, response.statusCode),
          code: response.statusCode || 200,
          timestamp: new Date().toISOString(),
        };
      }),
    );
  }

  /**
   * 根据 HTTP 方法和状态码生成成功消息
   */
  private getSuccessMessage(method: string, statusCode: number): string {
    switch (method) {
      case 'POST':
        return statusCode === 201 ? 'Resource created successfully' : 'Operation completed successfully';
      case 'PUT':
      case 'PATCH':
        return 'Resource updated successfully';
      case 'DELETE':
        return 'Resource deleted successfully';
      case 'GET':
      default:
        return 'Request completed successfully';
    }
  }
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 分页响应构建器
 */
export class PaginationBuilder {
  static build<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
  ): PaginatedResponse<T> {
    const totalPages = Math.ceil(total / limit);
    
    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }
}
