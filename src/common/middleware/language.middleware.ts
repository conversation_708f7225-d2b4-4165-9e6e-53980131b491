import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * 语言中间件
 * 从请求头中提取语言信息并设置到请求对象中
 */
@Injectable()
export class LanguageMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 从 Accept-Language 头部获取语言偏好
    const acceptLanguage = req.headers['accept-language'] || 'en-US';
    
    // 解析语言偏好，支持格式：zh-CN,en-US;q=0.9,en;q=0.8
    const languages = this.parseAcceptLanguage(acceptLanguage);
    
    // 设置默认语言为英文，支持的语言为中文和英文
    const supportedLanguages = ['zh-CN', 'en-US', 'zh', 'en'];
    const defaultLanguage = 'en-US';
    
    // 找到第一个支持的语言
    const selectedLanguage = languages.find(lang => 
      supportedLanguages.includes(lang)
    ) || defaultLanguage;
    
    // 将语言信息添加到请求对象中
    (req as any).language = selectedLanguage;
    (req as any).isChineseLocale = selectedLanguage.startsWith('zh');
    
    next();
  }

  /**
   * 解析 Accept-Language 头部
   * @param acceptLanguage Accept-Language 头部值
   * @returns 按优先级排序的语言数组
   */
  private parseAcceptLanguage(acceptLanguage: string): string[] {
    return acceptLanguage
      .split(',')
      .map(lang => {
        const [language, quality = 'q=1'] = lang.trim().split(';');
        const q = parseFloat(quality.replace('q=', ''));
        return { language: language.trim(), quality: q };
      })
      .sort((a, b) => b.quality - a.quality)
      .map(item => item.language);
  }
}

/**
 * 扩展 Request 接口以包含语言信息
 */
declare global {
  namespace Express {
    interface Request {
      language?: string;
      isChineseLocale?: boolean;
    }
  }
}
