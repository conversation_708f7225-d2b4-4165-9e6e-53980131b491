import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * 请求日志中间件
 * 记录所有 API 请求的详细信息，包括请求参数、响应时间等
 */
@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    const { method, originalUrl, ip, headers } = req;
    
    // 获取用户代理和真实IP
    const userAgent = headers['user-agent'] || '';
    const realIp = this.getRealIp(req);
    
    // 记录请求开始
    this.logger.log(`→ ${method} ${originalUrl} - ${realIp}`);

    // 监听响应完成事件
    res.on('finish', () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      const { statusCode } = res;
      
      // 构建日志信息
      const logData = {
        method,
        url: originalUrl,
        statusCode,
        duration: `${duration}ms`,
        ip: realIp,
        userAgent,
        timestamp: new Date().toISOString(),
      };

      // 根据状态码和响应时间选择日志级别
      const logMessage = `← ${method} ${originalUrl} ${statusCode} - ${duration}ms - ${realIp}`;
      
      if (statusCode >= 500) {
        this.logger.error(logMessage, JSON.stringify(logData));
      } else if (statusCode >= 400) {
        this.logger.warn(logMessage, JSON.stringify(logData));
      } else if (duration > 1000) {
        // 慢请求警告（超过1秒）
        this.logger.warn(`SLOW REQUEST: ${logMessage}`, JSON.stringify(logData));
      } else {
        this.logger.log(logMessage);
      }
    });

    // 监听请求错误
    res.on('error', (error) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.logger.error(
        `✗ ${method} ${originalUrl} - ${duration}ms - ${realIp}`,
        error.stack,
        JSON.stringify({
          method,
          url: originalUrl,
          duration: `${duration}ms`,
          ip: realIp,
          error: error.message,
          timestamp: new Date().toISOString(),
        }),
      );
    });

    next();
  }

  /**
   * 获取真实 IP 地址
   * 考虑代理服务器的情况
   */
  private getRealIp(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      (req.headers['x-real-ip'] as string) ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      req.ip ||
      'unknown'
    );
  }
}

/**
 * 请求上下文中间件
 * 为每个请求添加唯一的追踪ID
 */
@Injectable()
export class RequestContextMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    // 生成请求追踪ID
    const traceId = this.generateTraceId();
    
    // 将追踪ID添加到请求头中
    req.headers['x-trace-id'] = traceId;
    res.setHeader('X-Trace-ID', traceId);
    
    next();
  }

  /**
   * 生成追踪ID
   */
  private generateTraceId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
