import { OrderStatus, PaymentStatus, OrderSource, PaymentMethod } from '@prisma/client';

/**
 * 订单状态流转映射
 * 定义每个状态可以转换到的下一个状态
 */
export const ORDER_STATUS_TRANSITIONS: Record<OrderStatus, OrderStatus[]> = {
  [OrderStatus.PENDING_PAYMENT]: [
    OrderStatus.PAID,
    OrderStatus.CANCELLED,
  ],
  [OrderStatus.PAID]: [
    OrderStatus.PROCESSING,
    OrderStatus.CANCELLED,
    OrderStatus.REFUNDED,
  ],
  [OrderStatus.PROCESSING]: [
    OrderStatus.COMPLETED,
    OrderStatus.CANCELLED,
  ],
  [OrderStatus.COMPLETED]: [
    OrderStatus.REFUNDED, // 完成后可以退款
  ],
  [OrderStatus.CANCELLED]: [], // 已取消的订单不能再转换
  [OrderStatus.REFUNDED]: [], // 已退款的订单不能再转换
};

/**
 * 支付状态与订单状态的关联规则
 */
export const PAYMENT_ORDER_STATUS_MAPPING: Record<PaymentStatus, OrderStatus[]> = {
  [PaymentStatus.UNPAID]: [OrderStatus.PENDING_PAYMENT, OrderStatus.CANCELLED],
  [PaymentStatus.PAID]: [OrderStatus.PAID, OrderStatus.PROCESSING, OrderStatus.COMPLETED],
  [PaymentStatus.REFUNDED]: [OrderStatus.REFUNDED],
};

/**
 * 订单创建请求接口
 */
export interface CreateOrderRequest {
  customerId: string;
  source: OrderSource;
  note?: string;
  items: CreateOrderItemRequest[];
}

/**
 * 订单商品项创建请求接口
 */
export interface CreateOrderItemRequest {
  productId: string;
  quantity: number;
  selectedOptions?: string[]; // 选择的属性选项ID数组
}

/**
 * 订单状态更新请求接口
 */
export interface UpdateOrderStatusRequest {
  status: OrderStatus;
  handledBy?: string;
  note?: string;
}

/**
 * 支付请求接口
 */
export interface CreatePaymentRequest {
  orderId: string;
  amount: number;
  method: PaymentMethod;
  thirdPartyId?: string;
}

/**
 * 订单查询过滤器
 */
export interface OrderQueryFilter {
  tenantId: string;
  customerId?: string;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  source?: OrderSource;
  handledBy?: string;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
  search?: string; // 搜索订单号或客户信息
}

/**
 * 订单统计信息
 */
export interface OrderStatistics {
  totalOrders: number;
  totalAmount: number;
  statusCounts: Record<OrderStatus, number>;
  paymentStatusCounts: Record<PaymentStatus, number>;
  sourceCounts: Record<OrderSource, number>;
  averageOrderValue: number;
  todayOrders: number;
  todayAmount: number;
}

/**
 * 订单详情响应接口
 */
export interface OrderDetailResponse {
  id: string;
  orderNumber: string;
  tenantId: string;
  customerId: string;
  customerName?: string;
  customerPhone?: string;
  source: OrderSource;
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  note?: string;
  handledBy?: string;
  handlerName?: string;
  paidAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  items: OrderItemDetailResponse[];
  payments: PaymentDetailResponse[];
}

/**
 * 订单商品项详情响应接口
 */
export interface OrderItemDetailResponse {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  selectedOptions: OrderItemOptionResponse[];
}

/**
 * 订单商品项选项响应接口
 */
export interface OrderItemOptionResponse {
  id: string;
  optionId: string;
  optionName: string;
  price: number;
}

/**
 * 支付详情响应接口
 */
export interface PaymentDetailResponse {
  id: string;
  amount: number;
  method: PaymentMethod;
  status: PaymentStatus;
  thirdPartyId?: string;
  paidAt?: Date;
  createdAt: Date;
}

/**
 * 订单号生成配置
 */
export interface OrderNumberConfig {
  prefix: string; // 前缀，如 "SO" (SnackOrder)
  dateFormat: string; // 日期格式，如 "YYYYMMDD"
  sequenceLength: number; // 序列号长度
}

/**
 * 库存检查结果
 */
export interface StockCheckResult {
  productId: string;
  productName: string;
  requestedQuantity: number;
  availableStock: number | null; // null 表示不限库存
  isAvailable: boolean;
}

/**
 * 价格计算结果
 */
export interface PriceCalculationResult {
  subtotal: number; // 商品小计
  optionsTotal: number; // 选项附加费用
  itemTotal: number; // 商品项总计
}

/**
 * 订单验证结果
 */
export interface OrderValidationResult {
  isValid: boolean;
  errors: string[];
  stockChecks: StockCheckResult[];
  totalAmount: number;
}
