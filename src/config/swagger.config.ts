import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

/**
 * Swagger 文档配置
 */
export function setupSwagger(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('SnackOrder API')
    .setDescription(`
# SnackOrder 多租户点餐系统 API

## 概述
SnackOrder 是一个基于 NestJS 构建的多租户点餐系统后端服务，支持平台管理、商户管理和顾客端功能。

## 认证机制
系统使用 JWT (JSON Web Token) 进行身份认证，支持三种角色：
- **平台管理员** (admin-jwt): 管理整个平台和所有租户
- **商户用户** (tenant-jwt): 管理特定租户的业务数据
- **顾客** (customer-jwt): 访问店铺和下单功能

## 响应格式

### 成功响应
\`\`\`json
{
  "success": true,
  "data": {},
  "message": "Request completed successfully",
  "code": 200,
  "timestamp": "2024-01-01T00:00:00Z"
}
\`\`\`

### 错误响应
\`\`\`json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "email",
        "message": "Email format is invalid",
        "value": "invalid-email"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
\`\`\`

## 分页查询
所有列表接口都支持分页查询，使用以下参数：
- \`page\`: 页码，从1开始
- \`limit\`: 每页数量，默认20，最大100
- \`search\`: 搜索关键词（可选）
- \`status\`: 状态筛选（可选）

## 错误代码
系统使用标准化的错误代码，详细说明请参考错误代码表。

## 限流规则
- 全局限流：每IP每15分钟1000次请求
- 认证接口：每IP每15分钟10次登录尝试
- 短信接口：每手机号每分钟1次发送

## 多语言支持
API支持多语言，通过 \`Accept-Language\` 请求头指定语言偏好：
- \`zh-CN\`: 简体中文
- \`en-US\`: 英文（默认）
    `)
    .setVersion('1.0.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: '平台管理员 JWT Token',
        in: 'header',
      },
      'admin-jwt',
    )
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: '商户用户 JWT Token',
        in: 'header',
      },
      'tenant-jwt',
    )
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: '顾客 JWT Token',
        in: 'header',
      },
      'customer-jwt',
    )
    .addServer('http://localhost:3000/so-api', '开发环境')
    .addServer('https://api.snackorder.com/so-api', '生产环境')
    .addTag('系统健康检查', '系统状态和测试接口')
    .addTag('平台管理员', '平台管理员相关接口')
    .addTag('租户管理', '租户信息管理接口')
    .addTag('商户认证', '商户用户认证接口')
    .addTag('商户用户管理', '商户内部用户管理接口')
    .addTag('商品分类管理', '商品分类相关接口')
    .addTag('商品管理', '商品信息管理接口')
    .addTag('订单管理', '订单处理相关接口')
    .addTag('顾客认证', '顾客认证相关接口')
    .addTag('店铺浏览', '顾客浏览店铺接口')
    .addTag('顾客订单', '顾客下单相关接口')
    .addTag('短信服务', '短信验证码服务')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
  });

  // 自定义 CSS 样式
  const customCss = `
    .swagger-ui .topbar { display: none; }
    .swagger-ui .info .title { color: #3b82f6; }
    .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; border-radius: 8px; }
  `;

  SwaggerModule.setup('docs', app, document, {
    customCss,
    customSiteTitle: 'SnackOrder API Documentation',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      docExpansion: 'none',
      defaultModelsExpandDepth: 2,
      defaultModelExpandDepth: 2,
    },
  });
}

/**
 * 为不同的模块设置不同的 Swagger 文档
 */
export function setupModularSwagger(app: INestApplication): void {
  // 管理员 API 文档
  const adminConfig = new DocumentBuilder()
    .setTitle('SnackOrder Admin API')
    .setDescription('平台管理员专用 API 文档')
    .setVersion('1.0.0')
    .addBearerAuth(undefined, 'admin-jwt')
    .build();

  const adminDocument = SwaggerModule.createDocument(app, adminConfig, {
    include: [], // 这里可以指定只包含管理员相关的模块
  });

  SwaggerModule.setup('docs/admin', app, adminDocument);

  // 商户 API 文档
  const tenantConfig = new DocumentBuilder()
    .setTitle('SnackOrder Tenant API')
    .setDescription('商户管理专用 API 文档')
    .setVersion('1.0.0')
    .addBearerAuth(undefined, 'tenant-jwt')
    .build();

  const tenantDocument = SwaggerModule.createDocument(app, tenantConfig, {
    include: [], // 这里可以指定只包含商户相关的模块
  });

  SwaggerModule.setup('docs/tenant', app, tenantDocument);

  // 顾客 API 文档
  const customerConfig = new DocumentBuilder()
    .setTitle('SnackOrder Customer API')
    .setDescription('顾客端专用 API 文档')
    .setVersion('1.0.0')
    .addBearerAuth(undefined, 'customer-jwt')
    .build();

  const customerDocument = SwaggerModule.createDocument(app, customerConfig, {
    include: [], // 这里可以指定只包含顾客相关的模块
  });

  SwaggerModule.setup('docs/customer', app, customerDocument);
}

/**
 * 生成 OpenAPI JSON 文件
 */
export function generateOpenApiJson(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('SnackOrder API')
    .setDescription('SnackOrder 多租户点餐系统 API')
    .setVersion('1.0.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  
  // 可以将文档保存为 JSON 文件
  // fs.writeFileSync('./openapi.json', JSON.stringify(document, null, 2));
}
