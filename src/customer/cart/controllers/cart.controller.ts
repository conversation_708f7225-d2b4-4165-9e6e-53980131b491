import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Headers,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiHeader,
} from '@nestjs/swagger';
import { RateLimit } from '@/common/guards/rate-limit.guard';
import { CartService } from '../services/cart.service';
import {
  AddToCartDto,
  UpdateCartItemDto,
  UpdateCartDto,
  CartResponseDto,
  CartSummaryDto,
} from '../dto/cart.dto';

@ApiTags('购物车')
@Controller('cart')
export class CartController {
  constructor(private readonly cartService: CartService) {}

  @ApiOperation({ summary: '添加商品到购物车' })
  @ApiResponse({
    status: 201,
    description: '添加成功',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 400, description: '商品信息无效或库存不足' })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID，用于标识购物车',
    required: true,
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 30, // 最多30次添加
    message: 'Too many add to cart requests, please try again later',
  })
  @Post('add')
  addToCart(
    @Headers('x-session-id') sessionId: string,
    @Body() addToCartDto: AddToCartDto,
  ): Promise<CartResponseDto> {
    return this.cartService.addToCart(sessionId, addToCartDto);
  }

  @ApiOperation({ summary: '获取购物车' })
  @ApiResponse({
    status: 200,
    description: '获取购物车成功',
    type: CartResponseDto,
  })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID',
    required: true,
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 60, // 最多60次查询
    message: 'Too many cart requests, please try again later',
  })
  @Get()
  getCart(
    @Headers('x-session-id') sessionId: string,
  ): Promise<CartResponseDto | null> {
    return this.cartService.getCart(sessionId);
  }

  @ApiOperation({ summary: '获取购物车摘要' })
  @ApiResponse({
    status: 200,
    description: '获取购物车摘要成功',
    type: CartSummaryDto,
  })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID',
    required: true,
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 100, // 最多100次查询
    message: 'Too many cart summary requests, please try again later',
  })
  @Get('summary')
  getCartSummary(
    @Headers('x-session-id') sessionId: string,
  ): Promise<CartSummaryDto> {
    return this.cartService.getCartSummary(sessionId);
  }

  @ApiOperation({ summary: '更新购物车商品项' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 400, description: '更新信息无效或库存不足' })
  @ApiResponse({ status: 404, description: '购物车项不存在' })
  @ApiParam({ name: 'itemId', description: '购物车项ID' })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID',
    required: true,
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 50, // 最多50次更新
    message: 'Too many cart update requests, please try again later',
  })
  @Put('items/:itemId')
  updateCartItem(
    @Headers('x-session-id') sessionId: string,
    @Param('itemId') itemId: string,
    @Body() updateDto: UpdateCartItemDto,
  ): Promise<CartResponseDto> {
    return this.cartService.updateCartItem(sessionId, itemId, updateDto);
  }

  @ApiOperation({ summary: '删除购物车商品项' })
  @ApiResponse({
    status: 200,
    description: '删除成功',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 404, description: '购物车项不存在' })
  @ApiParam({ name: 'itemId', description: '购物车项ID' })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID',
    required: true,
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 50, // 最多50次删除
    message: 'Too many cart delete requests, please try again later',
  })
  @Delete('items/:itemId')
  removeCartItem(
    @Headers('x-session-id') sessionId: string,
    @Param('itemId') itemId: string,
  ): Promise<CartResponseDto> {
    return this.cartService.removeCartItem(sessionId, itemId);
  }

  @ApiOperation({ summary: '清空购物车' })
  @ApiResponse({ status: 204, description: '清空成功' })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID',
    required: true,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @Delete()
  clearCart(@Headers('x-session-id') sessionId: string): Promise<void> {
    return this.cartService.clearCart(sessionId);
  }

  @ApiOperation({ summary: '批量更新购物车' })
  @ApiResponse({
    status: 200,
    description: '批量更新成功',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 400, description: '更新信息无效' })
  @ApiParam({ name: 'tenantId', description: '商户ID' })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID',
    required: true,
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 10, // 最多10次批量更新
    message: 'Too many batch update requests, please try again later',
  })
  @Put('batch/:tenantId')
  updateCart(
    @Headers('x-session-id') sessionId: string,
    @Param('tenantId') tenantId: string,
    @Body() updateDto: UpdateCartDto,
  ): Promise<CartResponseDto> {
    return this.cartService.updateCart(sessionId, tenantId, updateDto);
  }
}
