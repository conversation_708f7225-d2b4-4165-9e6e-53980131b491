import {
  IsString,
  IsInt,
  IsArray,
  IsOptional,
  Min,
  ValidateNested,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 购物车商品项 DTO
 */
export class CartItemDto {
  @ApiProperty({ description: '商品ID' })
  @IsString()
  @IsUUID()
  productId: string;

  @ApiProperty({ description: '商品数量', minimum: 1 })
  @IsInt()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({ 
    description: '选择的属性选项ID数组',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  selectedOptions?: string[];
}

/**
 * 添加到购物车 DTO
 */
export class AddToCartDto {
  @ApiProperty({ description: '商户ID' })
  @IsString()
  @IsUUID()
  tenantId: string;

  @ApiProperty({ description: '商品ID' })
  @IsString()
  @IsUUID()
  productId: string;

  @ApiProperty({ description: '商品数量', minimum: 1 })
  @IsInt()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({ 
    description: '选择的属性选项ID数组',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  selectedOptions?: string[];
}

/**
 * 更新购物车商品 DTO
 */
export class UpdateCartItemDto {
  @ApiProperty({ description: '商品数量', minimum: 1 })
  @IsInt()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({ 
    description: '选择的属性选项ID数组',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  selectedOptions?: string[];
}

/**
 * 批量更新购物车 DTO
 */
export class UpdateCartDto {
  @ApiProperty({ 
    description: '购物车商品项列表',
    type: [CartItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CartItemDto)
  items: CartItemDto[];
}

/**
 * 购物车商品项响应 DTO
 */
export class CartItemResponseDto {
  @ApiProperty({ description: '购物车项ID' })
  id: string;

  @ApiProperty({ description: '商品ID' })
  productId: string;

  @ApiProperty({ description: '商品名称' })
  productName: string;

  @ApiProperty({ description: '商品描述' })
  productDescription?: string;

  @ApiProperty({ description: '商品图片' })
  productImage?: string;

  @ApiProperty({ description: '商品基础价格' })
  productPrice: number;

  @ApiProperty({ description: '商品数量' })
  quantity: number;

  @ApiProperty({ description: '商品小计（基础价格 × 数量）' })
  subtotal: number;

  @ApiProperty({ description: '选项附加费用总计' })
  optionsTotal: number;

  @ApiProperty({ description: '商品项总价（小计 + 选项费用）' })
  itemTotal: number;

  @ApiProperty({ description: '是否有库存' })
  inStock: boolean;

  @ApiProperty({ description: '库存数量（null表示不限库存）' })
  stock?: number;

  @ApiPropertyOptional({ 
    description: '选择的属性选项',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        price: { type: 'number' },
        attributeName: { type: 'string' },
      },
    },
  })
  selectedOptions?: Array<{
    id: string;
    name: string;
    price: number;
    attributeName: string;
  }>;

  @ApiProperty({ description: '添加时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * 购物车响应 DTO
 */
export class CartResponseDto {
  @ApiProperty({ description: '购物车ID' })
  id: string;

  @ApiProperty({ description: '商户ID' })
  tenantId: string;

  @ApiProperty({ description: '商户名称' })
  tenantName: string;

  @ApiProperty({ 
    description: '购物车商品项列表',
    type: [CartItemResponseDto],
  })
  items: CartItemResponseDto[];

  @ApiProperty({ description: '商品总数量' })
  totalQuantity: number;

  @ApiProperty({ description: '商品小计' })
  subtotal: number;

  @ApiProperty({ description: '选项附加费用总计' })
  optionsTotal: number;

  @ApiProperty({ description: '购物车总价' })
  total: number;

  @ApiProperty({ description: '是否所有商品都有库存' })
  allInStock: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * 购物车摘要 DTO
 */
export class CartSummaryDto {
  @ApiProperty({ description: '商品总数量' })
  totalQuantity: number;

  @ApiProperty({ description: '购物车总价' })
  total: number;

  @ApiProperty({ description: '商品种类数' })
  itemCount: number;

  @ApiProperty({ description: '是否有商品' })
  hasItems: boolean;
}
