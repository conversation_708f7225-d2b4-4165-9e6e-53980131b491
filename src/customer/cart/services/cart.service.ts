import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { GeneralStatus } from '@prisma/client';
import {
  ResourceNotFoundException,
  BusinessRuleViolationException,
  InsufficientStockException,
} from '@/common/exceptions/business.exceptions';
import {
  AddToCartDto,
  UpdateCartItemDto,
  UpdateCartDto,
  CartResponseDto,
  CartSummaryDto,
  CartItemResponseDto,
} from '../dto/cart.dto';

/**
 * 购物车商品项接口
 */
interface CartItem {
  id: string;
  productId: string;
  quantity: number;
  selectedOptions: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 购物车接口
 */
interface Cart {
  id: string;
  tenantId: string;
  items: CartItem[];
  createdAt: Date;
  updatedAt: Date;
}

@Injectable()
export class CartService {
  private readonly logger = new Logger(CartService.name);
  private readonly carts = new Map<string, Cart>(); // sessionId -> Cart

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 添加商品到购物车
   */
  async addToCart(
    sessionId: string,
    addToCartDto: AddToCartDto,
  ): Promise<CartResponseDto> {
    this.logger.log(
      `Adding product ${addToCartDto.productId} to cart for session ${sessionId}`,
    );

    // 验证商品
    const product = await this.validateProduct(
      addToCartDto.tenantId,
      addToCartDto.productId,
    );

    // 验证库存
    await this.validateStock(product, addToCartDto.quantity);

    // 验证属性选项
    if (addToCartDto.selectedOptions) {
      await this.validateProductOptions(product, addToCartDto.selectedOptions);
    }

    // 获取或创建购物车
    let cart = this.carts.get(sessionId);

    if (!cart) {
      cart = this.createNewCart(sessionId, addToCartDto.tenantId);
      this.carts.set(sessionId, cart);
    } else {
      // 验证商户一致性（一个购物车只能包含同一商户的商品）
      if (cart.tenantId !== addToCartDto.tenantId) {
        throw new BusinessRuleViolationException(
          'Cannot add products from different stores to the same cart',
          { currentTenant: cart.tenantId, newTenant: addToCartDto.tenantId },
        );
      }
    }

    // 检查是否已存在相同商品和选项的项
    const existingItemIndex = cart.items.findIndex(
      (item) =>
        item.productId === addToCartDto.productId &&
        this.arraysEqual(
          item.selectedOptions,
          addToCartDto.selectedOptions || [],
        ),
    );

    if (existingItemIndex >= 0) {
      // 更新数量
      cart.items[existingItemIndex].quantity += addToCartDto.quantity;
      cart.items[existingItemIndex].updatedAt = new Date();
    } else {
      // 添加新项
      const newItem: CartItem = {
        id: this.generateId(),
        productId: addToCartDto.productId,
        quantity: addToCartDto.quantity,
        selectedOptions: addToCartDto.selectedOptions || [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      cart.items.push(newItem);
    }

    cart.updatedAt = new Date();
    this.carts.set(sessionId, cart);

    return this.formatCartResponse(cart);
  }

  /**
   * 获取购物车
   */
  async getCart(sessionId: string): Promise<CartResponseDto | null> {
    this.logger.log(`Getting cart for session ${sessionId}`);

    const cart = this.carts.get(sessionId);
    if (!cart) {
      return null;
    }

    return this.formatCartResponse(cart);
  }

  /**
   * 获取购物车摘要
   */
  async getCartSummary(sessionId: string): Promise<CartSummaryDto> {
    this.logger.log(`Getting cart summary for session ${sessionId}`);

    const cart = this.carts.get(sessionId);
    if (!cart) {
      return {
        totalQuantity: 0,
        total: 0,
        itemCount: 0,
        hasItems: false,
      };
    }

    const cartResponse = await this.formatCartResponse(cart);

    return {
      totalQuantity: cartResponse.totalQuantity,
      total: cartResponse.total,
      itemCount: cartResponse.items.length,
      hasItems: cartResponse.items.length > 0,
    };
  }

  /**
   * 更新购物车商品项
   */
  async updateCartItem(
    sessionId: string,
    itemId: string,
    updateDto: UpdateCartItemDto,
  ): Promise<CartResponseDto> {
    this.logger.log(`Updating cart item ${itemId} for session ${sessionId}`);

    const cart = this.carts.get(sessionId);
    if (!cart) {
      throw new ResourceNotFoundException('Cart', sessionId);
    }

    const itemIndex = cart.items.findIndex((item) => item.id === itemId);
    if (itemIndex === -1) {
      throw new ResourceNotFoundException('CartItem', itemId);
    }

    const item = cart.items[itemIndex];

    // 验证商品和库存
    const product = await this.validateProduct(cart.tenantId, item.productId);
    await this.validateStock(product, updateDto.quantity);

    // 验证属性选项
    if (updateDto.selectedOptions) {
      await this.validateProductOptions(product, updateDto.selectedOptions);
    }

    // 更新商品项
    cart.items[itemIndex] = {
      ...item,
      quantity: updateDto.quantity,
      selectedOptions: updateDto.selectedOptions || item.selectedOptions,
      updatedAt: new Date(),
    };

    cart.updatedAt = new Date();
    this.carts.set(sessionId, cart);

    return this.formatCartResponse(cart);
  }

  /**
   * 删除购物车商品项
   */
  async removeCartItem(
    sessionId: string,
    itemId: string,
  ): Promise<CartResponseDto> {
    this.logger.log(`Removing cart item ${itemId} for session ${sessionId}`);

    const cart = this.carts.get(sessionId);
    if (!cart) {
      throw new ResourceNotFoundException('Cart', sessionId);
    }

    const itemIndex = cart.items.findIndex((item) => item.id === itemId);
    if (itemIndex === -1) {
      throw new ResourceNotFoundException('CartItem', itemId);
    }

    cart.items.splice(itemIndex, 1);
    cart.updatedAt = new Date();

    // 如果购物车为空，删除购物车
    if (cart.items.length === 0) {
      this.carts.delete(sessionId);
      return this.createEmptyCartResponse();
    }

    this.carts.set(sessionId, cart);
    return this.formatCartResponse(cart);
  }

  /**
   * 清空购物车
   */
  async clearCart(sessionId: string): Promise<void> {
    this.logger.log(`Clearing cart for session ${sessionId}`);
    this.carts.delete(sessionId);
  }

  /**
   * 批量更新购物车
   */
  async updateCart(
    sessionId: string,
    tenantId: string,
    updateDto: UpdateCartDto,
  ): Promise<CartResponseDto> {
    this.logger.log(`Updating cart for session ${sessionId}`);

    // 清空现有购物车
    this.carts.delete(sessionId);

    // 逐个添加商品
    for (const item of updateDto.items) {
      await this.addToCart(sessionId, {
        tenantId,
        productId: item.productId,
        quantity: item.quantity,
        selectedOptions: item.selectedOptions,
      });
    }

    const cart = this.carts.get(sessionId);
    return cart
      ? this.formatCartResponse(cart)
      : this.createEmptyCartResponse();
  }

  /**
   * 验证商品
   */
  private async validateProduct(tenantId: string, productId: string) {
    const product = await this.prisma.product.findFirst({
      where: {
        id: productId,
        tenantId,
        status: GeneralStatus.ACTIVE,
        deletedAt: null,
      },
      include: {
        attributes: {
          include: {
            options: true,
          },
        },
      },
    });

    if (!product) {
      throw new ResourceNotFoundException('Product', productId);
    }

    return product;
  }

  /**
   * 验证库存
   */
  private async validateStock(product: any, quantity: number): Promise<void> {
    if (product.stock !== null && product.stock < quantity) {
      throw new InsufficientStockException(product.id, quantity, product.stock);
    }
  }

  /**
   * 验证商品属性选项
   */
  private async validateProductOptions(
    product: any,
    selectedOptions: string[],
  ): Promise<void> {
    for (const optionId of selectedOptions) {
      const optionExists = product.attributes.some((attr) =>
        attr.options.some((option) => option.id === optionId),
      );

      if (!optionExists) {
        throw new ResourceNotFoundException('ProductOption', optionId);
      }
    }
  }

  /**
   * 创建新购物车
   */
  private createNewCart(sessionId: string, tenantId: string): Cart {
    return {
      id: this.generateId(),
      tenantId,
      items: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * 格式化购物车响应
   */
  private async formatCartResponse(cart: Cart): Promise<CartResponseDto> {
    // 获取商户信息
    const tenant = await this.prisma.tenant.findUnique({
      where: { id: cart.tenantId },
      select: { name: true },
    });

    // 获取所有商品信息
    const productIds = cart.items.map((item) => item.productId);
    const products = await this.prisma.product.findMany({
      where: {
        id: { in: productIds },
        status: GeneralStatus.ACTIVE,
        deletedAt: null,
      },
      include: {
        attributes: {
          include: {
            options: true,
          },
        },
      },
    });

    const items: CartItemResponseDto[] = [];
    let totalQuantity = 0;
    let subtotal = 0;
    let optionsTotal = 0;
    let allInStock = true;

    for (const cartItem of cart.items) {
      const product = products.find((p) => p.id === cartItem.productId);
      if (!product) continue;

      const itemSubtotal = Number(product.price) * cartItem.quantity;
      let itemOptionsTotal = 0;

      // 计算选项费用
      const selectedOptionDetails: Array<{
        id: string;
        name: string;
        price: number;
        attributeName: string;
      }> = [];

      for (const optionId of cartItem.selectedOptions) {
        for (const attribute of product.attributes) {
          const option = attribute.options.find((opt) => opt.id === optionId);
          if (option) {
            const optionPrice = Number(option.price) * cartItem.quantity;
            itemOptionsTotal += optionPrice;
            selectedOptionDetails.push({
              id: option.id,
              name: option.name,
              price: Number(option.price),
              attributeName: attribute.name,
            });
          }
        }
      }

      const itemTotal = itemSubtotal + itemOptionsTotal;
      const inStock =
        product.stock === null || product.stock >= cartItem.quantity;

      items.push({
        id: cartItem.id,
        productId: product.id,
        productName: product.name,
        productDescription: product.description || undefined,
        productImage: product.image || undefined,
        productPrice: Number(product.price),
        quantity: cartItem.quantity,
        subtotal: itemSubtotal,
        optionsTotal: itemOptionsTotal,
        itemTotal,
        inStock,
        stock: product.stock || undefined,
        selectedOptions: selectedOptionDetails,
        createdAt: cartItem.createdAt,
        updatedAt: cartItem.updatedAt,
      });

      totalQuantity += cartItem.quantity;
      subtotal += itemSubtotal;
      optionsTotal += itemOptionsTotal;

      if (!inStock) {
        allInStock = false;
      }
    }

    return {
      id: cart.id,
      tenantId: cart.tenantId,
      tenantName: tenant?.name || 'Unknown Store',
      items,
      totalQuantity,
      subtotal,
      optionsTotal,
      total: subtotal + optionsTotal,
      allInStock,
      createdAt: cart.createdAt,
      updatedAt: cart.updatedAt,
    };
  }

  /**
   * 创建空购物车响应
   */
  private createEmptyCartResponse(): CartResponseDto {
    return {
      id: '',
      tenantId: '',
      tenantName: '',
      items: [],
      totalQuantity: 0,
      subtotal: 0,
      optionsTotal: 0,
      total: 0,
      allInStock: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return `cart_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 比较数组是否相等
   */
  private arraysEqual(a: string[], b: string[]): boolean {
    if (a.length !== b.length) return false;
    const sortedA = [...a].sort();
    const sortedB = [...b].sort();
    return sortedA.every((val, index) => val === sortedB[index]);
  }
}
