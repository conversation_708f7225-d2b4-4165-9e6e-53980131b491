import {
  Controller,
  Post,
  Body,
  UnauthorizedException,
  Get,
  UseGuards,
  Request,
} from '@nestjs/common';
import { SmsService } from '@/sms/services/sms.service';
import { SendCodeDto } from '../dto/send-code.dto';
import { SmsCodeType } from '@prisma/client';
import { CustomerLoginDto } from '../dto/customer-login.dto';
import { CustomersService } from '@/customer/customers/services/customers.service';
import { CustomerAuthService } from '../services/customer-auth.service';
import { CustomerJwtAuthGuard } from '../guards/customer-jwt-auth.guard';

@Controller('customer/auth')
export class CustomerAuthController {
  constructor(
    private readonly smsService: SmsService,
    private readonly customersService: CustomersService,
    private readonly customerAuthService: CustomerAuthService,
  ) {}

  @Post('send-code')
  async sendCode(@Body() sendCodeDto: SendCodeDto) {
    // 为了防止接口被滥用，实际应用中这里需要增加速率限制 (Rate Limiting)
    const code = await this.smsService.sendCode(sendCodeDto.phone, SmsCodeType.LOGIN);
    // 在开发环境中返回验证码，方便测试
    return { success: true, code };
  }

  @Post('login')
  async login(@Body() loginDto: CustomerLoginDto) {
    const { phone, code, tenantId } = loginDto;

    const isValid = await this.smsService.verifyCode(phone, code, SmsCodeType.LOGIN);
    if (!isValid) {
      throw new UnauthorizedException('验证码不正确或已过期');
    }

    const customer = await this.customersService.findOrCreate(phone, tenantId);
    return this.customerAuthService.login(customer);
  }

  @UseGuards(CustomerJwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }
}
