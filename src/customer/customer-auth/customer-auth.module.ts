import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CustomersModule } from '../customers/customers.module';
import { SmsModule } from '@/sms/sms.module';
import { CustomerAuthService } from './services/customer-auth.service';
import { CustomerJwtStrategy } from './strategies/customer-jwt.strategy';
import { CustomerAuthController } from './controllers/customer-auth.controller';

@Module({
  imports: [
    CustomersModule,
    SmsModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('CUSTOMER_JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('CUSTOMER_JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [CustomerAuthController],
  providers: [CustomerAuthService, CustomerJwtStrategy],
  exports: [CustomerAuthService],
})
export class CustomerAuthModule {}
