import {
  IsNotEmpty,
  IsString,
  Length,
  Matches,
} from 'class-validator';

export class CustomerLoginDto {
  @IsString()
  @IsNotEmpty({ message: '手机号不能为空' })
  @Matches(/^1[3-9]\d{9}$/, { message: '手机号格式不正确' })
  phone: string;

  @IsString()
  @IsNotEmpty({ message: '验证码不能为空' })
  @Length(6, 6, { message: '验证码必须是6位' })
  code: string;

  @IsString()
  @IsNotEmpty({ message: '租户ID不能为空' })
  tenantId: string;
}
