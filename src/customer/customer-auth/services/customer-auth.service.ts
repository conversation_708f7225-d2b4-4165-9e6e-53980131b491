import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Customer } from '@prisma/client';

@Injectable()
export class CustomerAuthService {
  constructor(private readonly jwtService: JwtService) {}

  /**
   * 顾客登录并生成 JWT
   * @param customer 顾客信息
   * @returns 返回包含 access_token 的对象
   */
  async login(customer: Customer) {
    const payload = {
      phone: customer.phone,
      sub: customer.id,
      tenantId: customer.tenantId,
      isCustomer: true, // 在 payload 中明确标识这是顾客
    };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}
