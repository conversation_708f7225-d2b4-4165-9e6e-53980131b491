import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { CustomersService } from '@/customer/customers/services/customers.service';

// 策略的名称 'customer-jwt' 用于区分其他角色的 jwt 策略
@Injectable()
export class CustomerJwtStrategy extends PassportStrategy(
  Strategy,
  'customer-jwt',
) {
  constructor(
    private readonly configService: ConfigService,
    private readonly customersService: CustomersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey:
        configService.get<string>('CUSTOMER_JWT_SECRET') ||
        'customer-jwt-secret-fallback',
    });
  }

  /**
   * 验证 JWT 载荷
   * @param payload 解码后的 JWT 载荷
   * @returns 返回顾客信息，如果用户不存在或token不合法则抛出异常
   */
  async validate(payload: {
    sub: string;
    phone: string;
    tenantId: string;
    isCustomer: boolean;
  }) {
    // 验证这是否是一个顾客 token
    if (!payload.isCustomer) {
      throw new UnauthorizedException('无效的顾客凭证');
    }

    const customer = await this.customersService.findOneByPhone(
      payload.phone,
      payload.tenantId,
    );

    if (!customer) {
      throw new UnauthorizedException('顾客不存在或已被禁用');
    }

    return customer;
  }
}
