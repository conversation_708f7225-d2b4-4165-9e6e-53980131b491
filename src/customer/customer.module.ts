import { Module } from '@nestjs/common';
import { CustomerAuthModule } from './customer-auth/customer-auth.module';
import { CustomersModule } from './customers/customers.module';
import { StoreModule } from './store/store.module';
import { CartModule } from './cart/cart.module';
import { CustomerOrderModule } from './order/customer-order.module';

@Module({
  imports: [
    CustomerAuthModule,
    CustomersModule,
    StoreModule,
    CartModule,
    CustomerOrderModule,
  ],
  exports: [CustomerAuthModule, CustomersModule],
})
export class CustomerModule {}
