import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { Customer } from '@prisma/client';

@Injectable()
export class CustomersService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 根据手机号和租户ID查找顾客
   * @param phone 手机号
   * @param tenantId 租户ID
   * @returns 返回顾客信息，或在找不到时返回 null
   */
  async findOneByPhone(
    phone: string,
    tenantId: string,
  ): Promise<Customer | null> {
    return this.prisma.customer.findFirst({
      where: {
        phone,
        tenantId,
      },
    });
  }

  /**
   * 根据手机号和租户ID查找或创建顾客
   * @param phone 手机号
   * @param tenantId 租户ID
   * @returns 返回找到的或新创建的顾客信息
   */
  async findOrCreate(phone: string, tenantId: string): Promise<Customer> {
    const existingCustomer = await this.findOneByPhone(phone, tenantId);
    if (existingCustomer) {
      return existingCustomer;
    }

    return this.prisma.customer.create({
      data: {
        phone,
        tenantId,
      },
    });
  }
}
