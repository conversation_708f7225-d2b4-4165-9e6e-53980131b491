import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Headers,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiHeader,
} from '@nestjs/swagger';
import { RateLimit } from '@/common/guards/rate-limit.guard';
import { CustomerOrderService } from '../services/customer-order.service';
import {
  CreateCustomerOrderDto,
  CreateOrderFromCartDto,
  CustomerPayOrderDto,
  CustomerOrderResponseDto,
  OrderStatusDto,
} from '../dto/customer-order.dto';

@ApiTags('顾客订单')
@Controller('customer/orders')
export class CustomerOrderController {
  constructor(private readonly customerOrderService: CustomerOrderService) {}

  @ApiOperation({ summary: '创建订单' })
  @ApiResponse({ 
    status: 201, 
    description: '订单创建成功',
    type: CustomerOrderResponseDto,
  })
  @ApiResponse({ status: 400, description: '订单数据验证失败或商户不可用' })
  @ApiResponse({ status: 409, description: '库存不足' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 5, // 最多5个订单
    message: 'Too many orders created, please try again later',
  })
  @Post()
  createOrder(@Body() createOrderDto: CreateCustomerOrderDto): Promise<CustomerOrderResponseDto> {
    return this.customerOrderService.createOrder(createOrderDto);
  }

  @ApiOperation({ summary: '从购物车创建订单' })
  @ApiResponse({ 
    status: 201, 
    description: '订单创建成功',
    type: CustomerOrderResponseDto,
  })
  @ApiResponse({ status: 400, description: '购物车为空或商品缺货' })
  @ApiHeader({
    name: 'x-session-id',
    description: '会话ID，用于获取购物车',
    required: true,
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 5, // 最多5个订单
    message: 'Too many orders created, please try again later',
  })
  @Post('from-cart')
  createOrderFromCart(
    @Headers('x-session-id') sessionId: string,
    @Body() createOrderDto: CreateOrderFromCartDto,
  ): Promise<CustomerOrderResponseDto> {
    return this.customerOrderService.createOrderFromCart(sessionId, createOrderDto);
  }

  @ApiOperation({ summary: '获取订单详情' })
  @ApiResponse({ 
    status: 200, 
    description: '获取订单详情成功',
    type: CustomerOrderResponseDto,
  })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'tenantId', description: '商户ID' })
  @ApiParam({ name: 'orderId', description: '订单ID' })
  @ApiQuery({ name: 'phone', required: false, description: '顾客手机号（用于验证订单归属）' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 30, // 最多30次查询
    message: 'Too many order detail requests, please try again later',
  })
  @Get(':tenantId/:orderId')
  getOrderDetail(
    @Param('tenantId') tenantId: string,
    @Param('orderId') orderId: string,
    @Query('phone') customerPhone?: string,
  ): Promise<CustomerOrderResponseDto> {
    return this.customerOrderService.getOrderDetail(tenantId, orderId, customerPhone);
  }

  @ApiOperation({ summary: '获取订单状态' })
  @ApiResponse({ 
    status: 200, 
    description: '获取订单状态成功',
    type: OrderStatusDto,
  })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'tenantId', description: '商户ID' })
  @ApiParam({ name: 'orderId', description: '订单ID' })
  @ApiQuery({ name: 'phone', required: false, description: '顾客手机号（用于验证订单归属）' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 60, // 最多60次查询（状态查询频率较高）
    message: 'Too many order status requests, please try again later',
  })
  @Get(':tenantId/:orderId/status')
  getOrderStatus(
    @Param('tenantId') tenantId: string,
    @Param('orderId') orderId: string,
    @Query('phone') customerPhone?: string,
  ): Promise<OrderStatusDto> {
    return this.customerOrderService.getOrderStatus(tenantId, orderId, customerPhone);
  }

  @ApiOperation({ summary: '支付订单' })
  @ApiResponse({ 
    status: 200, 
    description: '支付成功',
    type: CustomerOrderResponseDto,
  })
  @ApiResponse({ status: 400, description: '订单状态不允许支付或支付失败' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'tenantId', description: '商户ID' })
  @ApiParam({ name: 'orderId', description: '订单ID' })
  @ApiQuery({ name: 'phone', required: false, description: '顾客手机号（用于验证订单归属）' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 10, // 最多10次支付尝试
    message: 'Too many payment attempts, please try again later',
  })
  @Post(':tenantId/:orderId/pay')
  payOrder(
    @Param('tenantId') tenantId: string,
    @Param('orderId') orderId: string,
    @Body() payOrderDto: CustomerPayOrderDto,
    @Query('phone') customerPhone?: string,
  ): Promise<CustomerOrderResponseDto> {
    return this.customerOrderService.payOrder(tenantId, orderId, payOrderDto, customerPhone);
  }

  @ApiOperation({ summary: '获取顾客订单列表' })
  @ApiResponse({ 
    status: 200, 
    description: '获取订单列表成功',
  })
  @ApiParam({ name: 'tenantId', description: '商户ID' })
  @ApiQuery({ name: 'phone', description: '顾客手机号' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 20, // 最多20次查询
    message: 'Too many order list requests, please try again later',
  })
  @Get(':tenantId/customer')
  getCustomerOrders(
    @Param('tenantId') tenantId: string,
    @Query('phone') customerPhone: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.customerOrderService.getCustomerOrders(
      tenantId,
      customerPhone,
      page ? parseInt(page.toString(), 10) : 1,
      limit ? parseInt(limit.toString(), 10) : 10,
    );
  }
}
