import { Module } from '@nestjs/common';
import { PrismaModule } from '@/common/prisma/prisma.module';
import { OrdersModule } from '@/tenant/orders/orders.module';
import { CartModule } from '../cart/cart.module';
import { CustomerOrderService } from './services/customer-order.service';
import { CustomerOrderController } from './controllers/customer-order.controller';

@Module({
  imports: [PrismaModule, OrdersModule, CartModule],
  controllers: [CustomerOrderController],
  providers: [CustomerOrderService],
  exports: [CustomerOrderService],
})
export class CustomerOrderModule {}
