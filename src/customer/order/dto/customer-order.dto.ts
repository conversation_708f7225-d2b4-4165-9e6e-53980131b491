import {
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  ValidateNested,
  IsPhoneNumber,
  IsUUID,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderSource, PaymentMethod } from '@prisma/client';

/**
 * 顾客信息 DTO
 */
export class CustomerInfoDto {
  @ApiProperty({ description: '顾客姓名' })
  @IsString()
  name: string;

  @ApiProperty({ description: '手机号' })
  @IsPhoneNumber('CN')
  phone: string;
}

/**
 * 订单商品项 DTO
 */
export class CustomerOrderItemDto {
  @ApiProperty({ description: '商品ID' })
  @IsString()
  @IsUUID()
  productId: string;

  @ApiProperty({ description: '商品数量', minimum: 1 })
  quantity: number;

  @ApiPropertyOptional({ 
    description: '选择的属性选项ID数组',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  selectedOptions?: string[];
}

/**
 * 顾客创建订单 DTO
 */
export class CreateCustomerOrderDto {
  @ApiProperty({ description: '商户ID' })
  @IsString()
  @IsUUID()
  tenantId: string;

  @ApiProperty({ 
    description: '顾客信息',
    type: CustomerInfoDto,
  })
  @ValidateNested()
  @Type(() => CustomerInfoDto)
  customer: CustomerInfoDto;

  @ApiProperty({ 
    description: '订单商品项列表',
    type: [CustomerOrderItemDto],
  })
  @IsArray()
  @ArrayMinSize(1, { message: '订单至少需要包含一个商品' })
  @ValidateNested({ each: true })
  @Type(() => CustomerOrderItemDto)
  items: CustomerOrderItemDto[];

  @ApiProperty({ 
    description: '订单来源',
    enum: OrderSource,
    default: OrderSource.MINIPROGRAM,
  })
  @IsEnum(OrderSource)
  source: OrderSource = OrderSource.MINIPROGRAM;

  @ApiPropertyOptional({ description: '订单备注' })
  @IsOptional()
  @IsString()
  note?: string;
}

/**
 * 从购物车创建订单 DTO
 */
export class CreateOrderFromCartDto {
  @ApiProperty({ description: '商户ID' })
  @IsString()
  @IsUUID()
  tenantId: string;

  @ApiProperty({ 
    description: '顾客信息',
    type: CustomerInfoDto,
  })
  @ValidateNested()
  @Type(() => CustomerInfoDto)
  customer: CustomerInfoDto;

  @ApiProperty({ 
    description: '订单来源',
    enum: OrderSource,
    default: OrderSource.MINIPROGRAM,
  })
  @IsEnum(OrderSource)
  source: OrderSource = OrderSource.MINIPROGRAM;

  @ApiPropertyOptional({ description: '订单备注' })
  @IsOptional()
  @IsString()
  note?: string;
}

/**
 * 顾客支付订单 DTO
 */
export class CustomerPayOrderDto {
  @ApiProperty({ 
    description: '支付方式',
    enum: PaymentMethod,
  })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiPropertyOptional({ description: '第三方支付平台返回的交易ID' })
  @IsOptional()
  @IsString()
  thirdPartyId?: string;
}

/**
 * 订单商品项响应 DTO
 */
export class CustomerOrderItemResponseDto {
  @ApiProperty({ description: '商品ID' })
  id: string;

  @ApiProperty({ description: '商品名称' })
  productName: string;

  @ApiProperty({ description: '商品数量' })
  quantity: number;

  @ApiProperty({ description: '商品单价' })
  unitPrice: number;

  @ApiProperty({ description: '商品总价' })
  totalPrice: number;

  @ApiPropertyOptional({ 
    description: '选择的属性选项',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        optionName: { type: 'string' },
        price: { type: 'number' },
      },
    },
  })
  selectedOptions?: Array<{
    id: string;
    optionName: string;
    price: number;
  }>;
}

/**
 * 顾客订单响应 DTO
 */
export class CustomerOrderResponseDto {
  @ApiProperty({ description: '订单ID' })
  id: string;

  @ApiProperty({ description: '订单号' })
  orderNumber: string;

  @ApiProperty({ description: '商户ID' })
  tenantId: string;

  @ApiProperty({ description: '商户名称' })
  tenantName: string;

  @ApiProperty({ description: '顾客ID' })
  customerId: string;

  @ApiProperty({ description: '顾客姓名' })
  customerName?: string;

  @ApiProperty({ description: '顾客手机号' })
  customerPhone?: string;

  @ApiProperty({ description: '订单来源', enum: OrderSource })
  source: OrderSource;

  @ApiProperty({ description: '订单总金额' })
  totalAmount: number;

  @ApiProperty({ description: '订单状态' })
  status: string;

  @ApiProperty({ description: '支付状态' })
  paymentStatus: string;

  @ApiPropertyOptional({ description: '订单备注' })
  note?: string;

  @ApiPropertyOptional({ description: '支付时间' })
  paidAt?: Date;

  @ApiPropertyOptional({ description: '完成时间' })
  completedAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ 
    description: '订单商品项列表',
    type: [CustomerOrderItemResponseDto],
  })
  items: CustomerOrderItemResponseDto[];

  @ApiPropertyOptional({ 
    description: '支付记录',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        amount: { type: 'number' },
        method: { type: 'string' },
        status: { type: 'string' },
        paidAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  payments?: Array<{
    id: string;
    amount: number;
    method: string;
    status: string;
    paidAt?: Date;
  }>;
}

/**
 * 订单状态查询 DTO
 */
export class OrderStatusDto {
  @ApiProperty({ description: '订单ID' })
  id: string;

  @ApiProperty({ description: '订单号' })
  orderNumber: string;

  @ApiProperty({ description: '订单状态' })
  status: string;

  @ApiProperty({ description: '支付状态' })
  paymentStatus: string;

  @ApiProperty({ description: '状态描述' })
  statusMessage: string;

  @ApiProperty({ description: '预计完成时间（分钟）' })
  estimatedTime?: number;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}
