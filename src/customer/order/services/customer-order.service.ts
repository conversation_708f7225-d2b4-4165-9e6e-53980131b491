import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { OrderStatus, PaymentStatus, OrderSource } from '@prisma/client';
import {
  ResourceNotFoundException,
  BusinessRuleViolationException,
} from '@/common/exceptions/business.exceptions';
import { OrderService } from '@/tenant/orders/services/order.service';
import { PaymentService } from '@/tenant/orders/services/payment.service';
import { CartService } from '@/customer/cart/services/cart.service';
import {
  CreateCustomerOrderDto,
  CreateOrderFromCartDto,
  CustomerPayOrderDto,
  CustomerOrderResponseDto,
  OrderStatusDto,
} from '../dto/customer-order.dto';

@Injectable()
export class CustomerOrderService {
  private readonly logger = new Logger(CustomerOrderService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly orderService: OrderService,
    private readonly paymentService: PaymentService,
    private readonly cartService: CartService,
  ) {}

  /**
   * 创建订单
   */
  async createOrder(
    createOrderDto: CreateCustomerOrderDto,
  ): Promise<CustomerOrderResponseDto> {
    this.logger.log(
      `Creating order for customer: ${createOrderDto.customer.phone}`,
    );

    // 验证商户状态
    await this.validateTenantForOrder(createOrderDto.tenantId);

    // 获取或创建顾客
    const customer = await this.getOrCreateCustomer(
      createOrderDto.tenantId,
      createOrderDto.customer,
    );

    // 转换为内部订单创建DTO
    const internalOrderDto = {
      customerId: customer.id,
      source: createOrderDto.source,
      note: createOrderDto.note,
      items: createOrderDto.items.map((item) => ({
        productId: item.productId,
        quantity: item.quantity,
        selectedOptions: item.selectedOptions || [],
      })),
    };

    // 使用现有的订单服务创建订单
    const order = await this.orderService.create(
      createOrderDto.tenantId,
      internalOrderDto,
    );

    return this.formatCustomerOrderResponse(order);
  }

  /**
   * 从购物车创建订单
   */
  async createOrderFromCart(
    sessionId: string,
    createOrderDto: CreateOrderFromCartDto,
  ): Promise<CustomerOrderResponseDto> {
    this.logger.log(`Creating order from cart for session: ${sessionId}`);

    // 获取购物车
    const cart = await this.cartService.getCart(sessionId);
    if (!cart || cart.items.length === 0) {
      throw new BusinessRuleViolationException('Cart is empty');
    }

    // 验证购物车商户与请求商户一致
    if (cart.tenantId !== createOrderDto.tenantId) {
      throw new BusinessRuleViolationException(
        'Cart tenant does not match order tenant',
        [
          {
            field: 'tenantId',
            message: 'Tenant mismatch between cart and order',
            value: {
              cartTenant: cart.tenantId,
              orderTenant: createOrderDto.tenantId,
            },
          },
        ],
      );
    }

    // 验证所有商品都有库存
    if (!cart.allInStock) {
      throw new BusinessRuleViolationException(
        'Some items in cart are out of stock',
      );
    }

    // 转换购物车项为订单项
    const orderItems = cart.items.map((item) => ({
      productId: item.productId,
      quantity: item.quantity,
      selectedOptions: item.selectedOptions?.map((opt) => opt.id) || [],
    }));

    // 创建订单
    const customerOrderDto: CreateCustomerOrderDto = {
      tenantId: createOrderDto.tenantId,
      customer: createOrderDto.customer,
      items: orderItems,
      source: createOrderDto.source,
      note: createOrderDto.note,
    };

    const order = await this.createOrder(customerOrderDto);

    // 清空购物车
    await this.cartService.clearCart(sessionId);

    return order;
  }

  /**
   * 获取订单详情
   */
  async getOrderDetail(
    tenantId: string,
    orderId: string,
    customerPhone?: string,
  ): Promise<CustomerOrderResponseDto> {
    this.logger.log(`Getting order detail: ${orderId}`);

    const order = await this.orderService.findOne(tenantId, orderId);

    // 如果提供了客户手机号，验证订单归属
    if (customerPhone) {
      const customer = await this.prisma.customer.findUnique({
        where: { id: order.customerId },
      });

      if (!customer || customer.phone !== customerPhone) {
        throw new ResourceNotFoundException('Order', orderId);
      }
    }

    return this.formatCustomerOrderResponse(order);
  }

  /**
   * 获取订单状态
   */
  async getOrderStatus(
    tenantId: string,
    orderId: string,
    customerPhone?: string,
  ): Promise<OrderStatusDto> {
    this.logger.log(`Getting order status: ${orderId}`);

    const order = await this.getOrderDetail(tenantId, orderId, customerPhone);

    return {
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      paymentStatus: order.paymentStatus,
      statusMessage: this.getStatusMessage(order.status, order.paymentStatus),
      estimatedTime: this.getEstimatedTime(order.status),
      updatedAt: order.updatedAt,
    };
  }

  /**
   * 支付订单
   */
  async payOrder(
    tenantId: string,
    orderId: string,
    payOrderDto: CustomerPayOrderDto,
    customerPhone?: string,
  ): Promise<CustomerOrderResponseDto> {
    this.logger.log(`Processing payment for order: ${orderId}`);

    // 验证订单归属
    const order = await this.getOrderDetail(tenantId, orderId, customerPhone);

    // 验证订单状态
    if (order.status !== OrderStatus.PENDING_PAYMENT) {
      throw new BusinessRuleViolationException(
        'Order is not in pending payment status',
        [
          {
            field: 'orderStatus',
            message: 'Invalid order status for payment',
            value: { currentStatus: order.status },
          },
        ],
      );
    }

    // 模拟第三方支付
    const paymentResult = await this.paymentService.simulateThirdPartyPayment(
      payOrderDto.paymentMethod,
      order.totalAmount,
    );

    if (!paymentResult.success) {
      throw new BusinessRuleViolationException('Payment failed', [
        {
          field: 'payment',
          message: 'Payment processing failed',
          value: { error: paymentResult.error },
        },
      ]);
    }

    // 创建支付记录
    await this.paymentService.createPayment(tenantId, {
      orderId,
      amount: order.totalAmount,
      method: payOrderDto.paymentMethod,
      thirdPartyId: paymentResult.transactionId,
    });

    // 返回更新后的订单信息
    return this.getOrderDetail(tenantId, orderId, customerPhone);
  }

  /**
   * 获取顾客订单列表
   */
  async getCustomerOrders(
    tenantId: string,
    customerPhone: string,
    page: number = 1,
    limit: number = 10,
  ) {
    this.logger.log(`Getting orders for customer: ${customerPhone}`);

    // 查找顾客
    const customer = await this.prisma.customer.findFirst({
      where: {
        tenantId,
        phone: customerPhone,
        deletedAt: null,
      },
    });

    if (!customer) {
      return {
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      };
    }

    // 获取订单列表
    const result = await this.orderService.findAll(tenantId, {
      customerId: customer.id,
      page,
      limit,
    });

    return {
      ...result,
      data: result.data.map((order) => this.formatCustomerOrderResponse(order)),
    };
  }

  /**
   * 验证商户可以接受订单
   */
  private async validateTenantForOrder(tenantId: string): Promise<void> {
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id: tenantId,
        deletedAt: null,
      },
      include: {
        settings: true,
      },
    });

    if (!tenant) {
      throw new ResourceNotFoundException('Store', tenantId);
    }

    if (tenant.status !== 'ACTIVE') {
      throw new BusinessRuleViolationException(
        'Store is not accepting orders',
        [
          {
            field: 'tenantStatus',
            message: 'Store is not active',
            value: { status: tenant.status },
          },
        ],
      );
    }

    // 检查是否允许在线下单
    const allowOnlineOrder = tenant.settings.find(
      (s) => s.key === 'allowOnlineOrder',
    )?.value;
    if (allowOnlineOrder === 'false') {
      throw new BusinessRuleViolationException(
        'Store is not accepting online orders',
      );
    }
  }

  /**
   * 获取或创建顾客
   */
  private async getOrCreateCustomer(tenantId: string, customerInfo: any) {
    let customer = await this.prisma.customer.findFirst({
      where: {
        tenantId,
        phone: customerInfo.phone,
        deletedAt: null,
      },
    });

    if (!customer) {
      customer = await this.prisma.customer.create({
        data: {
          tenantId,
          phone: customerInfo.phone,
          name: customerInfo.name,
        },
      });
    } else if (customerInfo.name && customer.name !== customerInfo.name) {
      // 更新顾客姓名
      customer = await this.prisma.customer.update({
        where: { id: customer.id },
        data: { name: customerInfo.name },
      });
    }

    return customer;
  }

  /**
   * 格式化顾客订单响应
   */
  private formatCustomerOrderResponse(order: any): CustomerOrderResponseDto {
    return {
      id: order.id,
      orderNumber: order.orderNumber,
      tenantId: order.tenantId,
      tenantName: order.tenant?.name || 'Unknown Store',
      customerId: order.customerId,
      customerName: order.customer?.name,
      customerPhone: order.customer?.phone,
      source: order.source,
      totalAmount: Number(order.totalAmount),
      status: order.status,
      paymentStatus: order.paymentStatus,
      note: order.note,
      paidAt: order.paidAt,
      completedAt: order.completedAt,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      items:
        order.items?.map((item) => ({
          id: item.id,
          productName: item.productName,
          quantity: item.quantity,
          unitPrice: Number(item.unitPrice),
          totalPrice: Number(item.totalPrice),
          selectedOptions:
            item.options?.map((opt) => ({
              id: opt.id,
              optionName: opt.optionName,
              price: Number(opt.price),
            })) || [],
        })) || [],
      payments:
        order.payments?.map((payment) => ({
          id: payment.id,
          amount: Number(payment.amount),
          method: payment.method,
          status: payment.status,
          paidAt: payment.paidAt,
        })) || [],
    };
  }

  /**
   * 获取状态描述
   */
  private getStatusMessage(status: string, paymentStatus: string): string {
    switch (status) {
      case OrderStatus.PENDING_PAYMENT:
        return '等待支付';
      case OrderStatus.PAID:
        return '支付成功，等待制作';
      case OrderStatus.PROCESSING:
        return '正在制作中';
      case OrderStatus.COMPLETED:
        return '订单已完成';
      case OrderStatus.CANCELLED:
        return '订单已取消';
      case OrderStatus.REFUNDED:
        return '订单已退款';
      default:
        return '未知状态';
    }
  }

  /**
   * 获取预计完成时间（分钟）
   */
  private getEstimatedTime(status: string): number | undefined {
    switch (status) {
      case OrderStatus.PAID:
        return 20; // 预计20分钟开始制作
      case OrderStatus.PROCESSING:
        return 15; // 预计15分钟完成制作
      default:
        return undefined;
    }
  }
}
