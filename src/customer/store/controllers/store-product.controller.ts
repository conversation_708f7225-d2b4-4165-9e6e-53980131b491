import {
  Controller,
  Get,
  Param,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { RateLimit } from '@/common/guards/rate-limit.guard';
import { StoreProductService } from '../services/store-product.service';
import {
  StoreCategoryDto,
  StoreProductDetailDto,
  StoreProductQueryDto,
  StoreMenuDto,
} from '../dto/store-product.dto';

@ApiTags('商户商品')
@Controller('store/:tenantId/products')
export class StoreProductController {
  constructor(private readonly storeProductService: StoreProductService) {}

  @ApiOperation({ summary: '获取商户菜单概览' })
  @ApiResponse({ 
    status: 200, 
    description: '获取菜单概览成功',
    type: StoreMenuDto,
  })
  @ApiResponse({ status: 404, description: '商户不存在' })
  @ApiParam({ 
    name: 'tenantId', 
    description: '商户ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 20, // 最多20次请求
    message: 'Too many requests for store menu, please try again later',
  })
  @Get('menu')
  getStoreMenu(@Param('tenantId') tenantId: string): Promise<StoreMenuDto> {
    return this.storeProductService.getStoreMenu(tenantId);
  }

  @ApiOperation({ summary: '获取商户分类列表' })
  @ApiResponse({ 
    status: 200, 
    description: '获取分类列表成功',
    type: [StoreCategoryDto],
  })
  @ApiResponse({ status: 404, description: '商户不存在' })
  @ApiParam({ 
    name: 'tenantId', 
    description: '商户ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 30, // 最多30次请求
    message: 'Too many requests for categories, please try again later',
  })
  @Get('categories')
  getStoreCategories(@Param('tenantId') tenantId: string): Promise<StoreCategoryDto[]> {
    return this.storeProductService.getStoreCategories(tenantId);
  }

  @ApiOperation({ summary: '获取商户商品列表' })
  @ApiResponse({ 
    status: 200, 
    description: '获取商品列表成功',
  })
  @ApiResponse({ status: 404, description: '商户不存在' })
  @ApiParam({ 
    name: 'tenantId', 
    description: '商户ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @ApiQuery({ name: 'categoryId', required: false, description: '分类ID' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'inStockOnly', required: false, description: '是否只显示有库存商品' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 50, // 最多50次请求
    message: 'Too many requests for products, please try again later',
  })
  @Get()
  getStoreProducts(
    @Param('tenantId') tenantId: string,
    @Query() queryDto: StoreProductQueryDto,
  ) {
    return this.storeProductService.getStoreProducts(tenantId, queryDto);
  }

  @ApiOperation({ summary: '获取商品详情' })
  @ApiResponse({ 
    status: 200, 
    description: '获取商品详情成功',
    type: StoreProductDetailDto,
  })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @ApiParam({ 
    name: 'tenantId', 
    description: '商户ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @ApiParam({ 
    name: 'productId', 
    description: '商品ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 100, // 最多100次请求
    message: 'Too many requests for product detail, please try again later',
  })
  @Get(':productId')
  getStoreProductDetail(
    @Param('tenantId') tenantId: string,
    @Param('productId') productId: string,
  ): Promise<StoreProductDetailDto> {
    return this.storeProductService.getStoreProductDetail(tenantId, productId);
  }
}
