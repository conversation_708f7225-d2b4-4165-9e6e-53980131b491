import {
  Controller,
  Get,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { RateLimit } from '@/common/guards/rate-limit.guard';
import { StoreService } from '../services/store.service';
import { StoreInfoDto, StoreStatusDto } from '../dto/store-info.dto';

@ApiTags('商户信息')
@Controller('store')
export class StoreController {
  constructor(private readonly storeService: StoreService) {}

  @ApiOperation({ summary: '通过 slug 获取商户信息' })
  @ApiResponse({ 
    status: 200, 
    description: '获取商户信息成功',
    type: StoreInfoDto,
  })
  @ApiResponse({ status: 404, description: '商户不存在' })
  @ApiParam({ 
    name: 'slug', 
    description: '商户路由标识符',
    example: 'kfc-wangfujing',
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 30, // 最多30次请求
    message: 'Too many requests for store info, please try again later',
  })
  @Get('slug/:slug')
  getStoreBySlug(@Param('slug') slug: string): Promise<StoreInfoDto> {
    return this.storeService.getStoreBySlug(slug);
  }

  @ApiOperation({ summary: '通过 ID 获取商户信息' })
  @ApiResponse({ 
    status: 200, 
    description: '获取商户信息成功',
    type: StoreInfoDto,
  })
  @ApiResponse({ status: 404, description: '商户不存在' })
  @ApiParam({ 
    name: 'id', 
    description: '商户ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 30, // 最多30次请求
    message: 'Too many requests for store info, please try again later',
  })
  @Get('id/:id')
  getStoreById(@Param('id') id: string): Promise<StoreInfoDto> {
    return this.storeService.getStoreById(id);
  }

  @ApiOperation({ summary: '获取商户营业状态' })
  @ApiResponse({ 
    status: 200, 
    description: '获取营业状态成功',
    type: StoreStatusDto,
  })
  @ApiResponse({ status: 404, description: '商户不存在' })
  @ApiParam({ 
    name: 'identifier', 
    description: '商户标识符（slug 或 ID）',
    example: 'kfc-wangfujing',
  })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 60, // 最多60次请求（状态查询频率较高）
    message: 'Too many requests for store status, please try again later',
  })
  @Get('status/:identifier')
  getStoreStatus(@Param('identifier') identifier: string): Promise<StoreStatusDto> {
    return this.storeService.getStoreStatus(identifier);
  }
}
