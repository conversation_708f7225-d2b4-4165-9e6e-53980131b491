import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TenantStatus } from '@prisma/client';

/**
 * 商户公开信息响应 DTO
 */
export class StoreInfoDto {
  @ApiProperty({ description: '商户ID' })
  id: string;

  @ApiProperty({ description: '商户名称' })
  name: string;

  @ApiProperty({ description: '路由标识符' })
  slug: string;

  @ApiPropertyOptional({ description: '商户描述' })
  description?: string;

  @ApiPropertyOptional({ description: '商户LOGO' })
  logo?: string;

  @ApiPropertyOptional({ description: '联系电话' })
  phone?: string;

  @ApiPropertyOptional({ description: '商户地址' })
  address?: string;

  @ApiProperty({ description: '商户状态', enum: TenantStatus })
  status: TenantStatus;

  @ApiProperty({ description: '是否营业中' })
  isOpen: boolean;

  @ApiPropertyOptional({ description: '营业时间信息' })
  businessHours?: {
    openTime: string;
    closeTime: string;
    isOpen24Hours: boolean;
  };

  @ApiPropertyOptional({ description: '商户设置' })
  settings?: {
    allowOnlineOrder: boolean;
    minOrderAmount: number;
    deliveryFee: number;
    estimatedDeliveryTime: number;
  };

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;
}

/**
 * 商户营业状态 DTO
 */
export class StoreStatusDto {
  @ApiProperty({ description: '商户ID' })
  id: string;

  @ApiProperty({ description: '商户名称' })
  name: string;

  @ApiProperty({ description: '是否营业中' })
  isOpen: boolean;

  @ApiProperty({ description: '商户状态', enum: TenantStatus })
  status: TenantStatus;

  @ApiPropertyOptional({ description: '状态说明' })
  statusMessage?: string;

  @ApiPropertyOptional({ description: '预计开业时间（如果当前休息）' })
  nextOpenTime?: string;
}
