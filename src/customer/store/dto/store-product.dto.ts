import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { GeneralStatus } from '@prisma/client';

/**
 * 商品属性选项 DTO
 */
export class ProductAttributeOptionDto {
  @ApiProperty({ description: '选项ID' })
  id: string;

  @ApiProperty({ description: '选项名称' })
  name: string;

  @ApiProperty({ description: '选项价格（附加费用）' })
  price: number;

  @ApiProperty({ description: '排序权重' })
  sort: number;
}

/**
 * 商品属性 DTO
 */
export class ProductAttributeDto {
  @ApiProperty({ description: '属性ID' })
  id: string;

  @ApiProperty({ description: '属性名称' })
  name: string;

  @ApiProperty({ description: '是否必选' })
  required: boolean;

  @ApiProperty({ description: '是否多选' })
  multiple: boolean;

  @ApiProperty({ description: '排序权重' })
  sort: number;

  @ApiProperty({ 
    description: '属性选项列表',
    type: [ProductAttributeOptionDto],
  })
  options: ProductAttributeOptionDto[];
}

/**
 * 商品分类 DTO
 */
export class StoreCategoryDto {
  @ApiProperty({ description: '分类ID' })
  id: string;

  @ApiProperty({ description: '分类名称' })
  name: string;

  @ApiProperty({ description: '排序权重' })
  sort: number;

  @ApiProperty({ description: '分类下商品数量' })
  productCount: number;
}

/**
 * 商品基本信息 DTO
 */
export class StoreProductDto {
  @ApiProperty({ description: '商品ID' })
  id: string;

  @ApiProperty({ description: '商品名称' })
  name: string;

  @ApiPropertyOptional({ description: '商品描述' })
  description?: string;

  @ApiProperty({ description: '商品价格' })
  price: number;

  @ApiPropertyOptional({ description: '商品图片' })
  image?: string;

  @ApiPropertyOptional({ description: '库存数量（null表示不限库存）' })
  stock?: number;

  @ApiProperty({ description: '是否有库存' })
  inStock: boolean;

  @ApiProperty({ description: '排序权重' })
  sort: number;

  @ApiProperty({ description: '商品状态', enum: GeneralStatus })
  status: GeneralStatus;

  @ApiPropertyOptional({ description: '分类信息' })
  category?: {
    id: string;
    name: string;
  };

  @ApiProperty({ description: '是否有属性选项' })
  hasAttributes: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;
}

/**
 * 商品详情 DTO
 */
export class StoreProductDetailDto extends StoreProductDto {
  @ApiProperty({ 
    description: '商品属性列表',
    type: [ProductAttributeDto],
  })
  attributes: ProductAttributeDto[];
}

/**
 * 商品列表查询 DTO
 */
export class StoreProductQueryDto {
  @ApiPropertyOptional({ description: '分类ID' })
  categoryId?: string;

  @ApiPropertyOptional({ description: '搜索关键词' })
  search?: string;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 20 })
  limit?: number = 20;

  @ApiPropertyOptional({ description: '是否只显示有库存的商品', default: false })
  inStockOnly?: boolean = false;
}

/**
 * 商品列表响应 DTO
 */
export class StoreProductListDto {
  @ApiProperty({ 
    description: '商品列表',
    type: [StoreProductDto],
  })
  data: StoreProductDto[];

  @ApiProperty({ description: '总记录数' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '是否有下一页' })
  hasNext: boolean;

  @ApiProperty({ description: '是否有上一页' })
  hasPrev: boolean;
}

/**
 * 商户商品概览 DTO
 */
export class StoreMenuDto {
  @ApiProperty({ 
    description: '商品分类列表',
    type: [StoreCategoryDto],
  })
  categories: StoreCategoryDto[];

  @ApiProperty({ 
    description: '推荐商品列表',
    type: [StoreProductDto],
  })
  featuredProducts: StoreProductDto[];

  @ApiProperty({ description: '商品总数' })
  totalProducts: number;

  @ApiProperty({ description: '有库存的商品数' })
  availableProducts: number;
}
