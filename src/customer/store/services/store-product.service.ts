import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { GeneralStatus, Prisma } from '@prisma/client';
import { ResourceNotFoundException } from '@/common/exceptions/business.exceptions';
import { PaginationBuilder } from '@/common/interceptors/response.interceptor';
import {
  StoreCategoryDto,
  StoreProductDto,
  StoreProductDetailDto,
  StoreProductQueryDto,
  StoreMenuDto,
} from '../dto/store-product.dto';

@Injectable()
export class StoreProductService {
  private readonly logger = new Logger(StoreProductService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 获取商户菜单概览
   */
  async getStoreMenu(tenantId: string): Promise<StoreMenuDto> {
    this.logger.log(`Getting store menu for tenant: ${tenantId}`);

    // 验证商户存在
    await this.validateTenant(tenantId);

    const [categories, featuredProducts, productStats] = await Promise.all([
      this.getStoreCategories(tenantId),
      this.getFeaturedProducts(tenantId),
      this.getProductStats(tenantId),
    ]);

    return {
      categories,
      featuredProducts,
      totalProducts: productStats.total,
      availableProducts: productStats.available,
    };
  }

  /**
   * 获取商户分类列表
   */
  async getStoreCategories(tenantId: string): Promise<StoreCategoryDto[]> {
    this.logger.log(`Getting categories for tenant: ${tenantId}`);

    await this.validateTenant(tenantId);

    const categories = await this.prisma.category.findMany({
      where: {
        tenantId,
        status: GeneralStatus.ACTIVE,
        deletedAt: null,
      },
      include: {
        _count: {
          select: {
            products: {
              where: {
                status: GeneralStatus.ACTIVE,
                deletedAt: null,
              },
            },
          },
        },
      },
      orderBy: [{ sort: 'asc' }, { createdAt: 'asc' }],
    });

    return categories.map((category) => ({
      id: category.id,
      name: category.name,
      sort: category.sort,
      productCount: category._count.products,
    }));
  }

  /**
   * 获取商户商品列表
   */
  async getStoreProducts(tenantId: string, queryDto: StoreProductQueryDto) {
    this.logger.log(`Getting products for tenant: ${tenantId}`);

    await this.validateTenant(tenantId);

    const { page = 1, limit = 20, categoryId, search, inStockOnly } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: Prisma.ProductWhereInput = {
      tenantId,
      status: GeneralStatus.ACTIVE,
      deletedAt: null,
      ...(categoryId && { categoryId }),
      ...(search && {
        name: {
          contains: search,
          mode: 'insensitive',
        },
      }),
      ...(inStockOnly && {
        OR: [
          { stock: null }, // 不限库存
          { stock: { gt: 0 } }, // 有库存
        ],
      }),
    };

    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          attributes: {
            select: {
              id: true,
            },
          },
        },
        orderBy: [{ sort: 'asc' }, { createdAt: 'desc' }],
        skip,
        take: limit,
      }),
      this.prisma.product.count({ where }),
    ]);

    const formattedProducts = products.map((product) =>
      this.formatProduct(product),
    );

    return PaginationBuilder.build(formattedProducts, total, page, limit);
  }

  /**
   * 获取商品详情
   */
  async getStoreProductDetail(
    tenantId: string,
    productId: string,
  ): Promise<StoreProductDetailDto> {
    this.logger.log(
      `Getting product detail: ${productId} for tenant: ${tenantId}`,
    );

    await this.validateTenant(tenantId);

    const product = await this.prisma.product.findFirst({
      where: {
        id: productId,
        tenantId,
        status: GeneralStatus.ACTIVE,
        deletedAt: null,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        attributes: {
          include: {
            options: {
              orderBy: [{ sort: 'asc' }, { createdAt: 'asc' }],
            },
          },
          orderBy: [{ sort: 'asc' }, { createdAt: 'asc' }],
        },
      },
    });

    if (!product) {
      throw new ResourceNotFoundException('Product', productId);
    }

    return this.formatProductDetail(product);
  }

  /**
   * 获取推荐商品
   */
  private async getFeaturedProducts(
    tenantId: string,
  ): Promise<StoreProductDto[]> {
    const products = await this.prisma.product.findMany({
      where: {
        tenantId,
        status: GeneralStatus.ACTIVE,
        deletedAt: null,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        attributes: {
          select: {
            id: true,
          },
        },
      },
      orderBy: [{ sort: 'asc' }, { createdAt: 'desc' }],
      take: 8, // 取前8个商品作为推荐
    });

    return products.map((product) => this.formatProduct(product));
  }

  /**
   * 获取商品统计信息
   */
  private async getProductStats(tenantId: string) {
    const [total, available] = await Promise.all([
      this.prisma.product.count({
        where: {
          tenantId,
          status: GeneralStatus.ACTIVE,
          deletedAt: null,
        },
      }),
      this.prisma.product.count({
        where: {
          tenantId,
          status: GeneralStatus.ACTIVE,
          deletedAt: null,
          OR: [
            { stock: null }, // 不限库存
            { stock: { gt: 0 } }, // 有库存
          ],
        },
      }),
    ]);

    return { total, available };
  }

  /**
   * 验证商户存在且可访问
   */
  private async validateTenant(tenantId: string): Promise<void> {
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id: tenantId,
        deletedAt: null,
      },
    });

    if (!tenant) {
      throw new ResourceNotFoundException('Store', tenantId);
    }
  }

  /**
   * 格式化商品信息
   */
  private formatProduct(product: any): StoreProductDto {
    return {
      id: product.id,
      name: product.name,
      description: product.description,
      price: Number(product.price),
      image: product.image,
      stock: product.stock,
      inStock: product.stock === null || product.stock > 0,
      sort: product.sort,
      status: product.status,
      category: product.category,
      hasAttributes: product.attributes && product.attributes.length > 0,
      createdAt: product.createdAt,
    };
  }

  /**
   * 格式化商品详情
   */
  private formatProductDetail(product: any): StoreProductDetailDto {
    const baseProduct = this.formatProduct(product);

    return {
      ...baseProduct,
      attributes: product.attributes.map((attr) => ({
        id: attr.id,
        name: attr.name,
        required: attr.required,
        multiple: attr.multiple,
        sort: attr.sort,
        options: attr.options.map((option) => ({
          id: option.id,
          name: option.name,
          price: Number(option.price),
          sort: option.sort,
        })),
      })),
    };
  }
}
