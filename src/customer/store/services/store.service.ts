import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { TenantStatus } from '@prisma/client';
import { ResourceNotFoundException } from '@/common/exceptions/business.exceptions';
import { StoreInfoDto, StoreStatusDto } from '../dto/store-info.dto';

@Injectable()
export class StoreService {
  private readonly logger = new Logger(StoreService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 通过 slug 获取商户信息
   */
  async getStoreBySlug(slug: string): Promise<StoreInfoDto> {
    this.logger.log(`Getting store info by slug: ${slug}`);

    const tenant = await this.prisma.tenant.findFirst({
      where: {
        slug,
        deletedAt: null,
      },
      include: {
        settings: true,
      },
    });

    if (!tenant) {
      throw new ResourceNotFoundException('Store', slug);
    }

    return this.formatStoreInfo(tenant);
  }

  /**
   * 通过 tenantId 获取商户信息
   */
  async getStoreById(tenantId: string): Promise<StoreInfoDto> {
    this.logger.log(`Getting store info by ID: ${tenantId}`);

    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id: tenantId,
        deletedAt: null,
      },
      include: {
        settings: true,
      },
    });

    if (!tenant) {
      throw new ResourceNotFoundException('Store', tenantId);
    }

    return this.formatStoreInfo(tenant);
  }

  /**
   * 获取商户营业状态
   */
  async getStoreStatus(identifier: string): Promise<StoreStatusDto> {
    this.logger.log(`Getting store status: ${identifier}`);

    // 尝试通过 slug 或 ID 查找
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        OR: [
          { slug: identifier },
          { id: identifier },
        ],
        deletedAt: null,
      },
      include: {
        settings: true,
      },
    });

    if (!tenant) {
      throw new ResourceNotFoundException('Store', identifier);
    }

    const isOpen = this.checkIfStoreIsOpen(tenant);
    const statusMessage = this.getStatusMessage(tenant.status, isOpen);
    const nextOpenTime = isOpen ? undefined : this.getNextOpenTime(tenant);

    return {
      id: tenant.id,
      name: tenant.name,
      isOpen,
      status: tenant.status,
      statusMessage,
      nextOpenTime,
    };
  }

  /**
   * 验证商户是否可以接受订单
   */
  async validateStoreForOrder(tenantId: string): Promise<boolean> {
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id: tenantId,
        deletedAt: null,
      },
      include: {
        settings: true,
      },
    });

    if (!tenant) {
      return false;
    }

    // 检查商户状态
    if (tenant.status !== TenantStatus.ACTIVE) {
      return false;
    }

    // 检查是否营业中
    if (!this.checkIfStoreIsOpen(tenant)) {
      return false;
    }

    // 检查是否允许在线下单
    const allowOnlineOrder = this.getSettingValue(
      tenant.settings,
      'allowOnlineOrder',
      'true',
    );
    if (allowOnlineOrder !== 'true') {
      return false;
    }

    return true;
  }

  /**
   * 格式化商户信息
   */
  private formatStoreInfo(tenant: any): StoreInfoDto {
    const isOpen = this.checkIfStoreIsOpen(tenant);
    const businessHours = this.getBusinessHours(tenant.settings);
    const storeSettings = this.getStoreSettings(tenant.settings);

    return {
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      description: tenant.description,
      logo: tenant.logo,
      phone: tenant.phone,
      address: tenant.address,
      status: tenant.status,
      isOpen,
      businessHours,
      settings: storeSettings,
      createdAt: tenant.createdAt,
    };
  }

  /**
   * 检查商户是否营业中
   */
  private checkIfStoreIsOpen(tenant: any): boolean {
    // 如果商户状态不是 ACTIVE，则不营业
    if (tenant.status !== TenantStatus.ACTIVE) {
      return false;
    }

    // 检查是否24小时营业
    const is24Hours = this.getSettingValue(
      tenant.settings,
      'is24Hours',
      'false',
    );
    if (is24Hours === 'true') {
      return true;
    }

    // 检查当前时间是否在营业时间内
    const openTime = this.getSettingValue(tenant.settings, 'openTime', '09:00');
    const closeTime = this.getSettingValue(tenant.settings, 'closeTime', '22:00');

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    return currentTime >= openTime && currentTime <= closeTime;
  }

  /**
   * 获取营业时间信息
   */
  private getBusinessHours(settings: any[]) {
    const openTime = this.getSettingValue(settings, 'openTime', '09:00');
    const closeTime = this.getSettingValue(settings, 'closeTime', '22:00');
    const is24Hours = this.getSettingValue(settings, 'is24Hours', 'false') === 'true';

    return {
      openTime,
      closeTime,
      isOpen24Hours: is24Hours,
    };
  }

  /**
   * 获取商户设置
   */
  private getStoreSettings(settings: any[]) {
    return {
      allowOnlineOrder: this.getSettingValue(settings, 'allowOnlineOrder', 'true') === 'true',
      minOrderAmount: parseFloat(this.getSettingValue(settings, 'minOrderAmount', '0')),
      deliveryFee: parseFloat(this.getSettingValue(settings, 'deliveryFee', '0')),
      estimatedDeliveryTime: parseInt(this.getSettingValue(settings, 'estimatedDeliveryTime', '30')),
    };
  }

  /**
   * 获取状态说明
   */
  private getStatusMessage(status: TenantStatus, isOpen: boolean): string {
    if (status !== TenantStatus.ACTIVE) {
      switch (status) {
        case TenantStatus.INACTIVE:
          return '商户暂停营业';
        case TenantStatus.SUSPENDED:
          return '商户已被暂停服务';
        default:
          return '商户当前不可用';
      }
    }

    return isOpen ? '正在营业中' : '当前休息中';
  }

  /**
   * 获取下次开业时间
   */
  private getNextOpenTime(tenant: any): string | undefined {
    if (tenant.status !== TenantStatus.ACTIVE) {
      return undefined;
    }

    const openTime = this.getSettingValue(tenant.settings, 'openTime', '09:00');
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(now.getDate() + 1);

    // 如果今天还没到开业时间，返回今天的开业时间
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    if (currentTime < openTime) {
      return `今天 ${openTime}`;
    }

    // 否则返回明天的开业时间
    return `明天 ${openTime}`;
  }

  /**
   * 获取设置值
   */
  private getSettingValue(settings: any[], key: string, defaultValue: string): string {
    const setting = settings?.find(s => s.key === key);
    return setting?.value || defaultValue;
  }
}
