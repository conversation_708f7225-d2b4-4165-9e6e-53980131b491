import { Module } from '@nestjs/common';
import { PrismaModule } from '@/common/prisma/prisma.module';
import { StoreService } from './services/store.service';
import { StoreProductService } from './services/store-product.service';
import { StoreController } from './controllers/store.controller';
import { StoreProductController } from './controllers/store-product.controller';

@Module({
  imports: [PrismaModule],
  controllers: [StoreController, StoreProductController],
  providers: [StoreService, StoreProductService],
  exports: [StoreService, StoreProductService],
})
export class StoreModule {}
