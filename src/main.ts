import 'tsconfig-paths/register';
import { NestFactory } from '@nestjs/core';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import { AppModule } from './app.module';
import { winstonConfig } from './config/winston.config';
import { setupSwagger } from './config/swagger.config';

async function bootstrap() {
  // 创建应用时直接使用 Winston 日志器 (bootstrapping 方式)
  const app = await NestFactory.create(AppModule, {
    logger: WinstonModule.createLogger(winstonConfig),
  });

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS 配置
  app.enableCors({
    origin:
      process.env.NODE_ENV === 'production'
        ? process.env.FRONTEND_DOMAIN?.split(',') || [
            'https://snackorder.ziotc.com',
          ]
        : true,
    credentials: true,
  });

  // API 全局前缀
  app.setGlobalPrefix('so-api');
  app.enableVersioning({
    type: VersioningType.URI,
  });

  // Swagger 文档配置
  if (process.env.NODE_ENV !== 'production') {
    setupSwagger(app);
  }

  const port = process.env.PORT || 3000;
  await app.listen(port);

  console.log(`🚀 应用已启动在端口 ${port}`);
  console.log(`📚 API 文档地址: http://localhost:${port}/docs`);
}

bootstrap();
