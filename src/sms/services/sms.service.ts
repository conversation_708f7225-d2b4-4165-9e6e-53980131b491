import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { SmsCodeType } from '@prisma/client';

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 生成、保存并“发送”验证码
   * @param phone 手机号
   * @param type 验证码类型
   * @returns 返回生成的验证码 (在实际应用中不应返回)
   */
  async sendCode(phone: string, type: SmsCodeType): Promise<string> {
    // 1. 生成一个6位数的随机数字验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    // 2. 设置过期时间（例如5分钟后）
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000);

    // 3. 将之前的同类型验证码标记为已使用 (可选，但推荐)
    await this.prisma.smsCode.updateMany({
      where: {
        phone,
        type,
        used: false,
      },
      data: {
        used: true,
      },
    });

    // 4. 将新验证码存入数据库
    await this.prisma.smsCode.create({
      data: {
        phone,
        code,
        type,
        expiresAt,
      },
    });

    // 5. 在开发环境中，直接打印验证码到控制台，模拟发送
    this.logger.log(`【模拟短信发送】手机号: ${phone}, 类型: ${type}, 验证码: ${code}`);

    // 注意：在生产环境中，这里应该是调用短信网关的API，并且不应该返回code
    return code;
  }

  /**
   * 校验验证码
   * @param phone 手机号
   * @param code 待验证的码
   * @param type 验证码类型
   * @returns 成功则返回 true, 失败则返回 false
   */
  async verifyCode(
    phone: string,
    code: string,
    type: SmsCodeType,
  ): Promise<boolean> {
    // 1. 查找最新的一条未使用、未过期的验证码
    const smsCode = await this.prisma.smsCode.findFirst({
      where: {
        phone,
        code,
        type,
        used: false,
        expiresAt: {
          gt: new Date(), // 检查是否过期
        },
      },
    });

    if (!smsCode) {
      return false;
    }

    // 2. 如果找到了，将其标记为已使用
    await this.prisma.smsCode.update({
      where: { id: smsCode.id },
      data: { used: true },
    });

    return true;
  }
}
