import {
  Controller,
  Post,
  UseGuards,
  Request,
  Get,
  Version,
  Body,
} from '@nestjs/common';
import { TenantAuthService } from '../services/tenant-auth.service';
import { TenantLocalAuthGuard } from '../guards/tenant-local-auth.guard';
import { TenantJwtAuthGuard } from '../guards/tenant-jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { LoginDto } from '../dto/login.dto';

@ApiTags('租户认证')
@Controller('tenant/auth')
export class TenantAuthController {
  constructor(private readonly tenantAuthService: TenantAuthService) {}

  @ApiOperation({ summary: '租户用户登录' })
  @ApiBody({ type: LoginDto })
  @UseGuards(TenantLocalAuthGuard)
  @Version('1')
  @Post('login')
  async login(@Body() loginDto: LoginDto, @Request() req) {
    return this.tenantAuthService.login(req.user);
  }

  @ApiOperation({ summary: '获取租户用户信息' })
  @ApiBearerAuth()
  @UseGuards(TenantJwtAuthGuard)
  @Version('1')
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }
}
