import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { TenantUsersService } from '@/tenant/users/services/tenant-users.service';

@Injectable()
export class TenantAuthService {
  constructor(
    private readonly tenantUsersService: TenantUsersService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 验证用户凭据
   * @param email 邮箱
   * @param pass 密码
   * @param tenantId 租户ID
   * @returns 成功则返回用户信息（不含密码），失败则返回 null
   */
  async validateUser(
    email: string,
    pass: string,
  ): Promise<any> {
    const user = await this.tenantUsersService.findOneByEmail(email);
    if (
      user &&
      (await this.tenantUsersService.validatePassword(pass, user.password))
    ) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  /**
   * 用户登录并生成 JWT
   * @param user 用户信息
   * @returns 返回包含 access_token 的对象
   */
  async login(user: any) {
    const payload = {
      email: user.email,
      sub: user.id,
      tenantId: user.tenantId,
      role: user.role,
    };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}
