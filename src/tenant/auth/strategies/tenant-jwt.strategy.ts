import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { TenantUsersService } from '../../users/services/tenant-users.service';

@Injectable()
export class TenantJwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly tenantUsersService: TenantUsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey:
        configService.get<string>('TENANT_JWT_SECRET') ||
        'tenant-jwt-secret-fallback',
    });
  }

  /**
   * 验证 JWT 载荷
   * @param payload 解码后的 JWT 载荷
   * @returns 返回用户信息，如果用户不存在则抛出异常
   */
  async validate(payload: { sub: string; email: string; tenantId: string }) {
    // sub 字段通常存储用户ID
    const user = await this.tenantUsersService.findOneByEmail(payload.email);

    if (!user) {
      throw new UnauthorizedException('用户不存在或已被禁用');
    }

    // Passport 会将此返回值附加到 Request 对象上，通常是 request.user
    // 这里我们剔除密码，避免敏感信息泄露
    const { password, ...result } = user;
    return result;
  }
}
