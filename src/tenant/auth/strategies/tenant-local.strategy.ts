import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { TenantAuthService } from '../services/tenant-auth.service';

@Injectable()
export class TenantLocalStrategy extends PassportStrategy(Strategy) {
  constructor(private tenantAuthService: TenantAuthService) {
    super({
      // 定义请求体中用户名字段的名称
      usernameField: 'email',
      // 定义请求体中密码字段的名称
      passwordField: 'password',
      // 将请求对象传递给 validate 回调
      passReqToCallback: true,
    });
  }

  /**
   * 验证用户
   * @param req 请求对象
   * @param email 用户邮箱
   * @param pass 密码
   * @returns 验证通过则返回用户信息，否则抛出异常
   */
  async validate(req, email: string, pass: string): Promise<any> {
    // passport-local 默认从 body 中获取 'username' 和 'password'
    // 但我们的 DTO 中没有 'username'，所以这里我们需要从请求对象中手动获取 tenantId
    const { tenantId } = req.body;

    if (!tenantId) {
      throw new UnauthorizedException('缺少租户ID (tenantId)');
    }

    const user = await this.tenantAuthService.validateUser(email, pass);
    if (!user) {
      throw new UnauthorizedException('邮箱、密码或租户不正确');
    }
    return user;
  }
}
