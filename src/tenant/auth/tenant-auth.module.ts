import { Modu<PERSON> } from '@nestjs/common';
import { TenantAuthService } from './services/tenant-auth.service';
import { TenantUsersModule } from '../users/tenant-users.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TenantJwtStrategy } from './strategies/tenant-jwt.strategy';
import { TenantLocalStrategy } from './strategies/tenant-local.strategy';
import { TenantAuthController } from './controllers/tenant-auth.controller';

@Module({
  imports: [
    TenantUsersModule,
    PassportModule,
    ConfigModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('TENANT_JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('TENANT_JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [TenantAuthController],
  providers: [TenantAuthService, TenantJwtStrategy, TenantLocalStrategy],
  exports: [TenantAuthService],
})
export class TenantAuthModule {}
