import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { CategoriesService } from './services/categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryQueryDto } from './dto/category-query.dto';
import { TenantJwtAuthGuard } from '../auth/guards/tenant-jwt-auth.guard';

@ApiTags('商品分类管理')
@ApiBearerAuth()
@UseGuards(TenantJwtAuthGuard)
@Controller('tenant/categories')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @ApiOperation({ summary: '创建商品分类' })
  @ApiResponse({ status: 201, description: '分类创建成功' })
  @Post()
  create(@Body() createCategoryDto: CreateCategoryDto, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.categoriesService.create(tenantId, createCategoryDto);
  }

  @ApiOperation({ summary: '获取分类列表' })
  @ApiResponse({ status: 200, description: '获取分类列表成功' })
  @Get()
  findAll(@Query() queryDto: CategoryQueryDto, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.categoriesService.findAll(tenantId, queryDto);
  }

  @ApiOperation({ summary: '获取分类详情' })
  @ApiResponse({ status: 200, description: '获取分类详情成功' })
  @ApiResponse({ status: 404, description: '分类不存在' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.categoriesService.findOne(tenantId, id);
  }

  @ApiOperation({ summary: '更新分类信息' })
  @ApiResponse({ status: 200, description: '分类更新成功' })
  @ApiResponse({ status: 404, description: '分类不存在' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
    @Request() req,
  ) {
    const tenantId = req.user.tenantId;
    return this.categoriesService.update(tenantId, id, updateCategoryDto);
  }

  @ApiOperation({ summary: '切换分类状态' })
  @ApiResponse({ status: 200, description: '状态切换成功' })
  @ApiResponse({ status: 404, description: '分类不存在' })
  @Patch(':id/toggle-status')
  toggleStatus(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.categoriesService.toggleStatus(tenantId, id);
  }

  @ApiOperation({ summary: '删除分类' })
  @ApiResponse({ status: 200, description: '分类删除成功' })
  @ApiResponse({ status: 404, description: '分类不存在或下有商品无法删除' })
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.categoriesService.remove(tenantId, id);
  }
}
