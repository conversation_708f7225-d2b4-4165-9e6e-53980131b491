import { IsOptional, IsEnum, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { GeneralStatus } from '@prisma/client';

export class CategoryQueryDto {
  @ApiPropertyOptional({ description: '分类状态', enum: GeneralStatus })
  @IsOptional()
  @IsEnum(GeneralStatus)
  status?: GeneralStatus;

  @ApiPropertyOptional({ description: '分类名称搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;
}
