import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { Category, GeneralStatus } from '@prisma/client';
import { CreateCategoryDto } from '../dto/create-category.dto';
import { UpdateCategoryDto } from '../dto/update-category.dto';
import { CategoryQueryDto } from '../dto/category-query.dto';

@Injectable()
export class CategoriesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 创建商品分类
   * @param tenantId 租户ID
   * @param createCategoryDto 创建分类数据
   * @returns 返回创建的分类信息
   */
  async create(
    tenantId: string,
    createCategoryDto: CreateCategoryDto,
  ): Promise<Category> {
    return this.prisma.category.create({
      data: {
        ...createCategoryDto,
        tenantId,
      },
    });
  }

  /**
   * 查询租户的所有分类
   * @param tenantId 租户ID
   * @param queryDto 查询条件
   * @returns 返回分类列表
   */
  async findAll(
    tenantId: string,
    queryDto: CategoryQueryDto = {},
  ): Promise<Category[]> {
    const { status, search } = queryDto;

    const where: any = {
      tenantId,
      deletedAt: null,
    };

    if (status) {
      where.status = status;
    }

    if (search) {
      where.name = {
        contains: search,
        mode: 'insensitive',
      };
    }

    return this.prisma.category.findMany({
      where,
      orderBy: [{ sort: 'desc' }, { createdAt: 'desc' }],
      include: {
        _count: {
          select: {
            products: {
              where: {
                deletedAt: null,
                status: GeneralStatus.ACTIVE,
              },
            },
          },
        },
      },
    });
  }

  /**
   * 根据ID查询分类
   * @param tenantId 租户ID
   * @param id 分类ID
   * @returns 返回分类信息
   */
  async findOne(tenantId: string, id: string): Promise<Category> {
    const category = await this.prisma.category.findFirst({
      where: {
        id,
        tenantId,
        deletedAt: null,
      },
      include: {
        _count: {
          select: {
            products: {
              where: {
                deletedAt: null,
                status: GeneralStatus.ACTIVE,
              },
            },
          },
        },
      },
    });

    if (!category) {
      throw new NotFoundException('分类不存在');
    }

    return category;
  }

  /**
   * 更新分类信息
   * @param tenantId 租户ID
   * @param id 分类ID
   * @param updateCategoryDto 更新数据
   * @returns 返回更新后的分类信息
   */
  async update(
    tenantId: string,
    id: string,
    updateCategoryDto: UpdateCategoryDto,
  ): Promise<Category> {
    await this.findOne(tenantId, id); // 验证分类存在

    return this.prisma.category.update({
      where: { id },
      data: updateCategoryDto,
    });
  }

  /**
   * 删除分类（软删除）
   * @param tenantId 租户ID
   * @param id 分类ID
   * @returns 返回删除结果
   */
  async remove(tenantId: string, id: string): Promise<Category> {
    await this.findOne(tenantId, id); // 验证分类存在

    // 检查是否有关联的商品
    const productCount = await this.prisma.product.count({
      where: {
        categoryId: id,
        deletedAt: null,
      },
    });

    if (productCount > 0) {
      throw new NotFoundException('该分类下还有商品，无法删除');
    }

    return this.prisma.category.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  /**
   * 切换分类状态
   * @param tenantId 租户ID
   * @param id 分类ID
   * @returns 返回更新后的分类信息
   */
  async toggleStatus(tenantId: string, id: string): Promise<Category> {
    const category = await this.findOne(tenantId, id);

    const newStatus =
      category.status === GeneralStatus.ACTIVE
        ? GeneralStatus.INACTIVE
        : GeneralStatus.ACTIVE;

    return this.prisma.category.update({
      where: { id },
      data: { status: newStatus },
    });
  }
}
