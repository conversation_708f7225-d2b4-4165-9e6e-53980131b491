import {
  Controller,
  Get,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { TenantJwtAuthGuard } from '@/tenant/auth/guards/tenant-jwt-auth.guard';
import { OrderStatisticsService } from '../services/order-statistics.service';

@ApiTags('订单统计')
@ApiBearerAuth()
@UseGuards(TenantJwtAuthGuard)
@Controller('tenant/order-statistics')
export class OrderStatisticsController {
  constructor(
    private readonly orderStatisticsService: OrderStatisticsService,
  ) {}

  @ApiOperation({ summary: '获取订单统计概览' })
  @ApiResponse({ status: 200, description: '获取统计数据成功' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  @Get('overview')
  getOrderStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Request() req?,
  ) {
    const tenantId = req.user.tenantId;
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.orderStatisticsService.getOrderStatistics(tenantId, start, end);
  }

  @ApiOperation({ summary: '获取热销商品统计' })
  @ApiResponse({ status: 200, description: '获取热销商品数据成功' })
  @ApiQuery({ name: 'limit', required: false, description: '返回数量限制' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  @Get('popular-products')
  getPopularProducts(
    @Query('limit') limit?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Request() req?,
  ) {
    const tenantId = req.user.tenantId;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.orderStatisticsService.getPopularProducts(
      tenantId,
      limitNum,
      start,
      end,
    );
  }

  @ApiOperation({ summary: '获取销售趋势数据' })
  @ApiResponse({ status: 200, description: '获取销售趋势数据成功' })
  @ApiQuery({ name: 'days', required: false, description: '统计天数' })
  @Get('sales-trend')
  getSalesTrend(
    @Query('days') days?: string,
    @Request() req?,
  ) {
    const tenantId = req.user.tenantId;
    const daysNum = days ? parseInt(days, 10) : 7;
    
    return this.orderStatisticsService.getSalesTrend(tenantId, daysNum);
  }

  @ApiOperation({ summary: '获取客户统计数据' })
  @ApiResponse({ status: 200, description: '获取客户统计数据成功' })
  @Get('customers')
  getCustomerStatistics(@Request() req) {
    const tenantId = req.user.tenantId;
    return this.orderStatisticsService.getCustomerStatistics(tenantId);
  }
}
