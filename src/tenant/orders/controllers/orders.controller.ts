import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { TenantJwtAuthGuard } from '@/tenant/auth/guards/tenant-jwt-auth.guard';
import { RateLimit } from '@/common/guards/rate-limit.guard';
import { OrderService } from '../services/order.service';
import { CreateOrderDto } from '../dto/create-order.dto';
import { UpdateOrderStatusDto } from '../dto/update-order.dto';
import { OrderQueryDto } from '../dto/order-query.dto';

@ApiTags('订单管理')
@ApiBearerAuth()
@UseGuards(TenantJwtAuthGuard)
@Controller('tenant/orders')
export class OrdersController {
  constructor(private readonly orderService: OrderService) {}

  @ApiOperation({ summary: '创建订单' })
  @ApiResponse({ status: 201, description: '订单创建成功' })
  @ApiResponse({ status: 400, description: '订单数据验证失败' })
  @ApiResponse({ status: 409, description: '库存不足' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 10, // 最多10个订单
    message: 'Too many orders created, please try again later',
  })
  @Post()
  create(@Body() createOrderDto: CreateOrderDto, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.orderService.create(tenantId, createOrderDto);
  }

  @ApiOperation({ summary: '获取订单列表' })
  @ApiResponse({ status: 200, description: '获取订单列表成功' })
  @Get()
  findAll(@Query() queryDto: OrderQueryDto, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.orderService.findAll(tenantId, queryDto);
  }

  @ApiOperation({ summary: '获取订单详情' })
  @ApiResponse({ status: 200, description: '获取订单详情成功' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'id', description: '订单ID' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.orderService.findOne(tenantId, id);
  }

  @ApiOperation({ summary: '更新订单状态' })
  @ApiResponse({ status: 200, description: '订单状态更新成功' })
  @ApiResponse({ status: 400, description: '状态转换不合法' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'id', description: '订单ID' })
  @Patch(':id/status')
  updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateOrderStatusDto,
    @Request() req,
  ) {
    const tenantId = req.user.tenantId;
    // 如果没有指定处理人，使用当前用户
    if (!updateStatusDto.handledBy) {
      updateStatusDto.handledBy = req.user.id;
    }
    return this.orderService.updateStatus(tenantId, id, updateStatusDto);
  }

  @ApiOperation({ summary: '取消订单' })
  @ApiResponse({ status: 200, description: '订单取消成功' })
  @ApiResponse({ status: 400, description: '订单状态不允许取消' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'id', description: '订单ID' })
  @HttpCode(HttpStatus.OK)
  @Patch(':id/cancel')
  cancel(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    const updateStatusDto: UpdateOrderStatusDto = {
      status: 'CANCELLED' as any,
      handledBy: req.user.id,
      note: 'Order cancelled by staff',
    };
    return this.orderService.updateStatus(tenantId, id, updateStatusDto);
  }

  @ApiOperation({ summary: '完成订单' })
  @ApiResponse({ status: 200, description: '订单完成成功' })
  @ApiResponse({ status: 400, description: '订单状态不允许完成' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'id', description: '订单ID' })
  @HttpCode(HttpStatus.OK)
  @Patch(':id/complete')
  complete(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    const updateStatusDto: UpdateOrderStatusDto = {
      status: 'COMPLETED' as any,
      handledBy: req.user.id,
      note: 'Order completed by staff',
    };
    return this.orderService.updateStatus(tenantId, id, updateStatusDto);
  }

  @ApiOperation({ summary: '开始制作订单' })
  @ApiResponse({ status: 200, description: '订单开始制作' })
  @ApiResponse({ status: 400, description: '订单状态不允许开始制作' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'id', description: '订单ID' })
  @HttpCode(HttpStatus.OK)
  @Patch(':id/process')
  startProcessing(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    const updateStatusDto: UpdateOrderStatusDto = {
      status: 'PROCESSING' as any,
      handledBy: req.user.id,
      note: 'Order processing started',
    };
    return this.orderService.updateStatus(tenantId, id, updateStatusDto);
  }
}
