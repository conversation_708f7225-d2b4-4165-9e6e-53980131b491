import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { TenantJwtAuthGuard } from '@/tenant/auth/guards/tenant-jwt-auth.guard';
import { RateLimit } from '@/common/guards/rate-limit.guard';
import { PaymentService } from '../services/payment.service';
import { CreatePaymentDto } from '../dto/create-payment.dto';

@ApiTags('支付管理')
@ApiBearerAuth()
@UseGuards(TenantJwtAuthGuard)
@Controller('tenant/payments')
export class PaymentsController {
  constructor(private readonly paymentService: PaymentService) {}

  @ApiOperation({ summary: '创建支付记录' })
  @ApiResponse({ status: 201, description: '支付记录创建成功' })
  @ApiResponse({ status: 400, description: '支付数据验证失败' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @RateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 20, // 最多20次支付请求
    message: 'Too many payment requests, please try again later',
  })
  @Post()
  create(@Body() createPaymentDto: CreatePaymentDto, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.paymentService.createPayment(tenantId, createPaymentDto);
  }

  @ApiOperation({ summary: '获取订单支付记录' })
  @ApiResponse({ status: 200, description: '获取支付记录成功' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'orderId', description: '订单ID' })
  @Get('order/:orderId')
  getOrderPayments(@Param('orderId') orderId: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.paymentService.getOrderPayments(tenantId, orderId);
  }

  @ApiOperation({ summary: '处理订单退款' })
  @ApiResponse({ status: 200, description: '退款处理成功' })
  @ApiResponse({ status: 400, description: '订单状态不允许退款' })
  @ApiResponse({ status: 404, description: '订单不存在' })
  @ApiParam({ name: 'orderId', description: '订单ID' })
  @HttpCode(HttpStatus.OK)
  @Post('refund/:orderId')
  processRefund(@Param('orderId') orderId: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.paymentService.processRefund(tenantId, orderId);
  }
}
