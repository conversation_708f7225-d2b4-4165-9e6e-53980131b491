import {
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  ValidateNested,
  IsInt,
  Min,
  ArrayMinSize,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderSource } from '@prisma/client';

/**
 * 创建订单商品项 DTO
 */
export class CreateOrderItemDto {
  @ApiProperty({
    description: '商品ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @IsString()
  @IsUUID()
  productId: string;

  @ApiProperty({
    description: '商品数量',
    example: 2,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({
    description: '选择的属性选项ID数组',
    type: [String],
    example: ['option-id-1', 'option-id-2'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  selectedOptions?: string[];
}

/**
 * 创建订单 DTO
 */
export class CreateOrderDto {
  @ApiProperty({
    description: '客户ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @IsString()
  @IsUUID()
  customerId: string;

  @ApiProperty({
    description: '订单来源',
    enum: OrderSource,
    example: OrderSource.MINIPROGRAM,
  })
  @IsEnum(OrderSource)
  source: OrderSource;

  @ApiPropertyOptional({
    description: '订单备注',
    example: '少糖，不要冰',
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: '订单商品项列表',
    type: [CreateOrderItemDto],
  })
  @IsArray()
  @ArrayMinSize(1, { message: '订单至少需要包含一个商品' })
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  items: CreateOrderItemDto[];
}
