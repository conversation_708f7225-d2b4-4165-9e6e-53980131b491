import {
  IsS<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Min,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentMethod } from '@prisma/client';

/**
 * 创建支付记录 DTO
 */
export class CreatePaymentDto {
  @ApiProperty({
    description: '订单ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @IsString()
  @IsUUID()
  orderId: string;

  @ApiProperty({
    description: '支付金额',
    example: 29.99,
    minimum: 0.01,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  amount: number;

  @ApiProperty({
    description: '支付方式',
    enum: PaymentMethod,
    example: PaymentMethod.WECHAT,
  })
  @IsEnum(PaymentMethod)
  method: PaymentMethod;

  @ApiPropertyOptional({
    description: '第三方支付平台交易ID',
    example: 'wx_pay_123456789',
  })
  @IsOptional()
  @IsString()
  thirdPartyId?: string;
}
