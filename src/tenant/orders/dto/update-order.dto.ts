import {
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus } from '@prisma/client';

/**
 * 更新订单状态 DTO
 */
export class UpdateOrderStatusDto {
  @ApiPropertyOptional({
    description: '订单状态',
    enum: OrderStatus,
    example: OrderStatus.PROCESSING,
  })
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @ApiPropertyOptional({
    description: '处理人员ID',
    example: 'clxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  handledBy?: string;

  @ApiPropertyOptional({
    description: '状态更新备注',
    example: '开始制作',
  })
  @IsOptional()
  @IsString()
  note?: string;
}

/**
 * 更新订单备注 DTO
 */
export class UpdateOrderNoteDto {
  @ApiPropertyOptional({
    description: '订单备注',
    example: '客户要求加急处理',
  })
  @IsOptional()
  @IsString()
  note?: string;
}
