import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '@/common/prisma/prisma.module';
import { OrderService } from './services/order.service';
import { PaymentService } from './services/payment.service';
import { OrderStatisticsService } from './services/order-statistics.service';
import { OrdersController } from './controllers/orders.controller';
import { PaymentsController } from './controllers/payments.controller';
import { OrderStatisticsController } from './controllers/order-statistics.controller';

@Module({
  imports: [PrismaModule],
  controllers: [
    OrdersController,
    PaymentsController,
    OrderStatisticsController,
  ],
  providers: [OrderService, PaymentService, OrderStatisticsService],
  exports: [OrderService, PaymentService, OrderStatisticsService],
})
export class OrdersModule {}
