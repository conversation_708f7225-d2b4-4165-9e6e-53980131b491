import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { OrderStatus, PaymentStatus, OrderSource } from '@prisma/client';
import { OrderStatistics } from '@/common/types/order.types';

@Injectable()
export class OrderStatisticsService {
  private readonly logger = new Logger(OrderStatisticsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 获取订单统计信息
   */
  async getOrderStatistics(
    tenantId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<OrderStatistics> {
    const dateFilter = this.buildDateFilter(startDate, endDate);

    // 并行查询各种统计数据
    const [
      totalOrders,
      totalAmountResult,
      statusCounts,
      paymentStatusCounts,
      sourceCounts,
      todayStats,
    ] = await Promise.all([
      // 总订单数
      this.prisma.order.count({
        where: { tenantId, ...dateFilter },
      }),

      // 总金额
      this.prisma.order.aggregate({
        where: { tenantId, ...dateFilter },
        _sum: { totalAmount: true },
      }),

      // 按状态统计
      this.getOrderCountsByStatus(tenantId, dateFilter),

      // 按支付状态统计
      this.getOrderCountsByPaymentStatus(tenantId, dateFilter),

      // 按来源统计
      this.getOrderCountsBySource(tenantId, dateFilter),

      // 今日统计
      this.getTodayStatistics(tenantId),
    ]);

    const totalAmount = Number(totalAmountResult._sum.totalAmount || 0);
    const averageOrderValue = totalOrders > 0 ? totalAmount / totalOrders : 0;

    return {
      totalOrders,
      totalAmount,
      statusCounts,
      paymentStatusCounts,
      sourceCounts,
      averageOrderValue: Math.round(averageOrderValue * 100) / 100, // 保留两位小数
      todayOrders: todayStats.count,
      todayAmount: todayStats.amount,
    };
  }

  /**
   * 获取热销商品统计
   */
  async getPopularProducts(
    tenantId: string,
    limit: number = 10,
    startDate?: Date,
    endDate?: Date,
  ) {
    const dateFilter = this.buildDateFilter(startDate, endDate);

    return this.prisma.orderItem.groupBy({
      by: ['productId', 'productName'],
      where: {
        order: {
          tenantId,
          ...dateFilter,
        },
      },
      _sum: {
        quantity: true,
        totalPrice: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        _sum: {
          quantity: 'desc',
        },
      },
      take: limit,
    });
  }

  /**
   * 获取销售趋势数据
   */
  async getSalesTrend(tenantId: string, days: number = 7) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days + 1);
    startDate.setHours(0, 0, 0, 0);

    const salesData = await this.prisma.order.groupBy({
      by: ['createdAt'],
      where: {
        tenantId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _sum: {
        totalAmount: true,
      },
      _count: {
        id: true,
      },
    });

    // 按日期分组
    const trendData: Array<{
      date: string;
      totalAmount: number;
      orderCount: number;
    }> = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      const dayData = salesData.filter(
        (item) => item.createdAt.toISOString().split('T')[0] === dateStr,
      );

      const dayTotal = dayData.reduce(
        (sum, item) => sum + Number(item._sum.totalAmount || 0),
        0,
      );
      const dayCount = dayData.reduce((sum, item) => sum + item._count.id, 0);

      trendData.push({
        date: dateStr,
        totalAmount: dayTotal,
        orderCount: dayCount,
      });
    }

    return trendData;
  }

  /**
   * 获取客户统计
   */
  async getCustomerStatistics(tenantId: string) {
    const [totalCustomers, activeCustomers, topCustomers] = await Promise.all([
      // 总客户数
      this.prisma.customer.count({
        where: { tenantId, deletedAt: null },
      }),

      // 活跃客户数（30天内有订单）
      this.prisma.customer.count({
        where: {
          tenantId,
          deletedAt: null,
          lastOrderAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),

      // 消费最多的客户
      this.prisma.customer.findMany({
        where: { tenantId, deletedAt: null },
        orderBy: { totalAmount: 'desc' },
        take: 10,
        select: {
          id: true,
          name: true,
          phone: true,
          totalOrders: true,
          totalAmount: true,
          lastOrderAt: true,
        },
      }),
    ]);

    return {
      totalCustomers,
      activeCustomers,
      topCustomers,
    };
  }

  /**
   * 按状态统计订单数量
   */
  private async getOrderCountsByStatus(tenantId: string, dateFilter: any) {
    const results = await this.prisma.order.groupBy({
      by: ['status'],
      where: { tenantId, ...dateFilter },
      _count: { id: true },
    });

    const statusCounts: Record<OrderStatus, number> = {
      [OrderStatus.PENDING_PAYMENT]: 0,
      [OrderStatus.PAID]: 0,
      [OrderStatus.PROCESSING]: 0,
      [OrderStatus.COMPLETED]: 0,
      [OrderStatus.CANCELLED]: 0,
      [OrderStatus.REFUNDED]: 0,
    };

    results.forEach((result) => {
      statusCounts[result.status] = result._count.id;
    });

    return statusCounts;
  }

  /**
   * 按支付状态统计订单数量
   */
  private async getOrderCountsByPaymentStatus(
    tenantId: string,
    dateFilter: any,
  ) {
    const results = await this.prisma.order.groupBy({
      by: ['paymentStatus'],
      where: { tenantId, ...dateFilter },
      _count: { id: true },
    });

    const paymentStatusCounts: Record<PaymentStatus, number> = {
      [PaymentStatus.UNPAID]: 0,
      [PaymentStatus.PAID]: 0,
      [PaymentStatus.REFUNDED]: 0,
    };

    results.forEach((result) => {
      paymentStatusCounts[result.paymentStatus] = result._count.id;
    });

    return paymentStatusCounts;
  }

  /**
   * 按来源统计订单数量
   */
  private async getOrderCountsBySource(tenantId: string, dateFilter: any) {
    const results = await this.prisma.order.groupBy({
      by: ['source'],
      where: { tenantId, ...dateFilter },
      _count: { id: true },
    });

    const sourceCounts: Record<OrderSource, number> = {
      [OrderSource.MINIPROGRAM]: 0,
      [OrderSource.H5]: 0,
      [OrderSource.APP]: 0,
      [OrderSource.POS]: 0,
    };

    results.forEach((result) => {
      sourceCounts[result.source] = result._count.id;
    });

    return sourceCounts;
  }

  /**
   * 获取今日统计
   */
  private async getTodayStatistics(tenantId: string) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const result = await this.prisma.order.aggregate({
      where: {
        tenantId,
        createdAt: {
          gte: today,
          lt: tomorrow,
        },
      },
      _count: { id: true },
      _sum: { totalAmount: true },
    });

    return {
      count: result._count.id,
      amount: Number(result._sum.totalAmount || 0),
    };
  }

  /**
   * 构建日期过滤条件
   */
  private buildDateFilter(startDate?: Date, endDate?: Date) {
    if (!startDate && !endDate) {
      return {};
    }

    const filter: any = {};
    if (startDate) {
      filter.createdAt = { ...filter.createdAt, gte: startDate };
    }
    if (endDate) {
      filter.createdAt = { ...filter.createdAt, lte: endDate };
    }

    return filter;
  }
}
