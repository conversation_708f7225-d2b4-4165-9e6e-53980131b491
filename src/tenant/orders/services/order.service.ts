import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { OrderStatus, PaymentStatus, Prisma } from '@prisma/client';
import {
  ResourceNotFoundException,
  BusinessRuleViolationException,
  InvalidOrderStatusException,
  InsufficientStockException,
} from '@/common/exceptions/business.exceptions';
import { PaginationBuilder } from '@/common/interceptors/response.interceptor';
import { CreateOrderDto } from '../dto/create-order.dto';
import { UpdateOrderStatusDto } from '../dto/update-order.dto';
import { OrderQueryDto } from '../dto/order-query.dto';
import {
  ORDER_STATUS_TRANSITIONS,
  OrderValidationResult,
  StockCheckResult,
  PriceCalculationResult,
} from '@/common/types/order.types';

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 创建订单
   */
  async create(tenantId: string, createOrderDto: CreateOrderDto) {
    this.logger.log(`Creating order for tenant ${tenantId}`);

    // 1. 验证订单数据
    const validation = await this.validateOrder(tenantId, createOrderDto);
    if (!validation.isValid) {
      throw new BusinessRuleViolationException(
        'Order validation failed',
        { errors: validation.errors },
      );
    }

    // 2. 检查库存
    const insufficientStock = validation.stockChecks.filter(
      (check) => !check.isAvailable,
    );
    if (insufficientStock.length > 0) {
      const stockError = insufficientStock[0];
      throw new InsufficientStockException(
        stockError.productId,
        stockError.requestedQuantity,
        stockError.availableStock || 0,
      );
    }

    // 3. 生成订单号
    const orderNumber = await this.generateOrderNumber(tenantId);

    // 4. 创建订单（使用事务）
    return this.prisma.$transaction(async (tx) => {
      // 创建订单主记录
      const order = await tx.order.create({
        data: {
          tenantId,
          customerId: createOrderDto.customerId,
          orderNumber,
          source: createOrderDto.source,
          totalAmount: validation.totalAmount,
          status: OrderStatus.PENDING_PAYMENT,
          paymentStatus: PaymentStatus.UNPAID,
          note: createOrderDto.note,
        },
      });

      // 创建订单商品项
      for (const itemDto of createOrderDto.items) {
        const product = await tx.product.findFirst({
          where: {
            id: itemDto.productId,
            tenantId,
            deletedAt: null,
          },
          include: {
            attributes: {
              include: {
                options: true,
              },
            },
          },
        });

        if (!product) {
          throw new ResourceNotFoundException('Product', itemDto.productId);
        }

        // 计算商品项价格
        const priceCalc = this.calculateItemPrice(
          product,
          itemDto.quantity,
          itemDto.selectedOptions || [],
        );

        // 创建订单商品项
        const orderItem = await tx.orderItem.create({
          data: {
            orderId: order.id,
            productId: product.id,
            productName: product.name,
            quantity: itemDto.quantity,
            unitPrice: product.price,
            totalPrice: priceCalc.itemTotal,
          },
        });

        // 创建选择的属性选项记录
        if (itemDto.selectedOptions && itemDto.selectedOptions.length > 0) {
          for (const optionId of itemDto.selectedOptions) {
            const option = await tx.productAttributeOption.findUnique({
              where: { id: optionId },
            });

            if (option) {
              await tx.orderItemAttributeOption.create({
                data: {
                  orderItemId: orderItem.id,
                  optionId: option.id,
                  optionName: option.name,
                  price: option.price,
                },
              });
            }
          }
        }

        // 更新库存（如果商品有库存限制）
        if (product.stock !== null) {
          await tx.product.update({
            where: { id: product.id },
            data: {
              stock: {
                decrement: itemDto.quantity,
              },
            },
          });
        }
      }

      // 更新客户统计信息
      await tx.customer.update({
        where: { id: createOrderDto.customerId },
        data: {
          totalOrders: { increment: 1 },
          totalAmount: { increment: validation.totalAmount },
          lastOrderAt: new Date(),
        },
      });

      this.logger.log(`Order created successfully: ${order.orderNumber}`);
      return this.findOne(tenantId, order.id);
    });
  }

  /**
   * 获取订单详情
   */
  async findOne(tenantId: string, orderId: string) {
    const order = await this.prisma.order.findFirst({
      where: {
        id: orderId,
        tenantId,
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        handler: {
          select: {
            id: true,
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
              },
            },
            options: {
              include: {
                option: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
        payments: true,
      },
    });

    if (!order) {
      throw new ResourceNotFoundException('Order', orderId);
    }

    return order;
  }

  /**
   * 获取订单列表
   */
  async findAll(tenantId: string, queryDto: OrderQueryDto) {
    const { page = 1, limit = 20, ...filters } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: Prisma.OrderWhereInput = {
      tenantId,
      ...(filters.customerId && { customerId: filters.customerId }),
      ...(filters.status && { status: filters.status }),
      ...(filters.paymentStatus && { paymentStatus: filters.paymentStatus }),
      ...(filters.source && { source: filters.source }),
      ...(filters.handledBy && { handledBy: filters.handledBy }),
      ...(filters.startDate &&
        filters.endDate && {
          createdAt: {
            gte: new Date(filters.startDate),
            lte: new Date(filters.endDate),
          },
        }),
      ...(filters.search && {
        OR: [
          { orderNumber: { contains: filters.search, mode: 'insensitive' } },
          {
            customer: {
              OR: [
                { name: { contains: filters.search, mode: 'insensitive' } },
                { phone: { contains: filters.search, mode: 'insensitive' } },
              ],
            },
          },
        ],
      }),
    };

    // 查询订单列表
    const [orders, total] = await Promise.all([
      this.prisma.order.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
          handler: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              items: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.order.count({ where }),
    ]);

    return PaginationBuilder.build(orders, total, page, limit);
  }

  /**
   * 更新订单状态
   */
  async updateStatus(
    tenantId: string,
    orderId: string,
    updateDto: UpdateOrderStatusDto,
  ) {
    const order = await this.findOne(tenantId, orderId);

    // 验证状态转换是否合法
    const allowedTransitions = ORDER_STATUS_TRANSITIONS[order.status];
    if (!allowedTransitions.includes(updateDto.status)) {
      throw new InvalidOrderStatusException(
        orderId,
        order.status,
        updateDto.status,
      );
    }

    // 更新订单状态
    const updatedOrder = await this.prisma.order.update({
      where: { id: orderId },
      data: {
        status: updateDto.status,
        handledBy: updateDto.handledBy,
        ...(updateDto.status === OrderStatus.COMPLETED && {
          completedAt: new Date(),
        }),
        ...(updateDto.note && { note: updateDto.note }),
      },
    });

    this.logger.log(
      `Order ${order.orderNumber} status updated from ${order.status} to ${updateDto.status}`,
    );

    return this.findOne(tenantId, orderId);
  }

  /**
   * 验证订单数据
   */
  private async validateOrder(
    tenantId: string,
    createOrderDto: CreateOrderDto,
  ): Promise<OrderValidationResult> {
    const errors: string[] = [];
    const stockChecks: StockCheckResult[] = [];
    let totalAmount = 0;

    // 验证客户是否存在
    const customer = await this.prisma.customer.findFirst({
      where: {
        id: createOrderDto.customerId,
        tenantId,
        deletedAt: null,
      },
    });

    if (!customer) {
      errors.push(`Customer ${createOrderDto.customerId} not found`);
    }

    // 验证商品和计算价格
    for (const itemDto of createOrderDto.items) {
      const product = await this.prisma.product.findFirst({
        where: {
          id: itemDto.productId,
          tenantId,
          deletedAt: null,
          status: 'ACTIVE',
        },
        include: {
          attributes: {
            include: {
              options: true,
            },
          },
        },
      });

      if (!product) {
        errors.push(`Product ${itemDto.productId} not found or inactive`);
        continue;
      }

      // 检查库存
      const stockCheck: StockCheckResult = {
        productId: product.id,
        productName: product.name,
        requestedQuantity: itemDto.quantity,
        availableStock: product.stock,
        isAvailable:
          product.stock === null || product.stock >= itemDto.quantity,
      };
      stockChecks.push(stockCheck);

      // 计算价格
      const priceCalc = this.calculateItemPrice(
        product,
        itemDto.quantity,
        itemDto.selectedOptions || [],
      );
      totalAmount += priceCalc.itemTotal;
    }

    return {
      isValid: errors.length === 0,
      errors,
      stockChecks,
      totalAmount,
    };
  }

  /**
   * 计算商品项价格
   */
  private calculateItemPrice(
    product: any,
    quantity: number,
    selectedOptionIds: string[],
  ): PriceCalculationResult {
    const subtotal = Number(product.price) * quantity;
    let optionsTotal = 0;

    // 计算选项附加费用
    for (const attribute of product.attributes) {
      for (const option of attribute.options) {
        if (selectedOptionIds.includes(option.id)) {
          optionsTotal += Number(option.price) * quantity;
        }
      }
    }

    return {
      subtotal,
      optionsTotal,
      itemTotal: subtotal + optionsTotal,
    };
  }

  /**
   * 生成订单号
   */
  private async generateOrderNumber(tenantId: string): Promise<string> {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD
    const prefix = `SO${dateStr}`;

    // 查找今天的最大序号
    const lastOrder = await this.prisma.order.findFirst({
      where: {
        tenantId,
        orderNumber: {
          startsWith: prefix,
        },
      },
      orderBy: {
        orderNumber: 'desc',
      },
    });

    let sequence = 1;
    if (lastOrder) {
      const lastSequence = parseInt(
        lastOrder.orderNumber.slice(prefix.length),
        10,
      );
      sequence = lastSequence + 1;
    }

    return `${prefix}${sequence.toString().padStart(4, '0')}`;
  }
}
