import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { OrderStatus, PaymentStatus } from '@prisma/client';
import {
  ResourceNotFoundException,
  BusinessRuleViolationException,
  PaymentFailedException,
} from '@/common/exceptions/business.exceptions';
import { CreatePaymentDto } from '../dto/create-payment.dto';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 创建支付记录
   */
  async createPayment(tenantId: string, createPaymentDto: CreatePaymentDto) {
    this.logger.log(`Creating payment for order ${createPaymentDto.orderId}`);

    // 验证订单
    const order = await this.prisma.order.findFirst({
      where: {
        id: createPaymentDto.orderId,
        tenantId,
      },
    });

    if (!order) {
      throw new ResourceNotFoundException('Order', createPaymentDto.orderId);
    }

    // 验证订单状态
    if (order.status !== OrderStatus.PENDING_PAYMENT) {
      throw new BusinessRuleViolationException(
        'Order is not in pending payment status',
        { currentStatus: order.status },
      );
    }

    // 验证支付金额
    if (createPaymentDto.amount !== Number(order.totalAmount)) {
      throw new BusinessRuleViolationException(
        'Payment amount does not match order total',
        {
          orderTotal: Number(order.totalAmount),
          paymentAmount: createPaymentDto.amount,
        },
      );
    }

    // 创建支付记录（使用事务）
    return this.prisma.$transaction(async (tx) => {
      // 创建支付记录
      const payment = await tx.payment.create({
        data: {
          orderId: createPaymentDto.orderId,
          amount: createPaymentDto.amount,
          method: createPaymentDto.method,
          status: PaymentStatus.PAID,
          thirdPartyId: createPaymentDto.thirdPartyId,
          paidAt: new Date(),
        },
      });

      // 更新订单状态
      await tx.order.update({
        where: { id: createPaymentDto.orderId },
        data: {
          status: OrderStatus.PAID,
          paymentStatus: PaymentStatus.PAID,
          paidAt: new Date(),
        },
      });

      this.logger.log(
        `Payment created successfully for order ${order.orderNumber}`,
      );
      return payment;
    });
  }

  /**
   * 获取订单的支付记录
   */
  async getOrderPayments(tenantId: string, orderId: string) {
    // 验证订单存在
    const order = await this.prisma.order.findFirst({
      where: {
        id: orderId,
        tenantId,
      },
    });

    if (!order) {
      throw new ResourceNotFoundException('Order', orderId);
    }

    return this.prisma.payment.findMany({
      where: { orderId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * 处理退款
   */
  async processRefund(
    tenantId: string,
    orderId: string,
    refundAmount?: number,
  ) {
    this.logger.log(`Processing refund for order ${orderId}`);

    const order = await this.prisma.order.findFirst({
      where: {
        id: orderId,
        tenantId,
      },
      include: {
        payments: true,
      },
    });

    if (!order) {
      throw new ResourceNotFoundException('Order', orderId);
    }

    // 验证订单可以退款
    const refundableStatuses: OrderStatus[] = [
      OrderStatus.PAID,
      OrderStatus.PROCESSING,
      OrderStatus.COMPLETED,
    ];
    if (!refundableStatuses.includes(order.status)) {
      throw new BusinessRuleViolationException(
        'Order cannot be refunded in current status',
        { currentStatus: order.status },
      );
    }

    // 计算退款金额
    const totalPaid = order.payments
      .filter((p) => p.status === PaymentStatus.PAID)
      .reduce((sum, p) => sum + Number(p.amount), 0);

    const actualRefundAmount = refundAmount || totalPaid;

    if (actualRefundAmount > totalPaid) {
      throw new BusinessRuleViolationException(
        'Refund amount cannot exceed paid amount',
        { paidAmount: totalPaid, refundAmount: actualRefundAmount },
      );
    }

    // 处理退款（使用事务）
    return this.prisma.$transaction(async (tx) => {
      // 创建退款记录
      const refundPayment = await tx.payment.create({
        data: {
          orderId,
          amount: -actualRefundAmount, // 负数表示退款
          method: order.payments[0]?.method || 'WECHAT', // 使用原支付方式
          status: PaymentStatus.REFUNDED,
          paidAt: new Date(),
        },
      });

      // 更新订单状态
      await tx.order.update({
        where: { id: orderId },
        data: {
          status: OrderStatus.REFUNDED,
          paymentStatus: PaymentStatus.REFUNDED,
        },
      });

      this.logger.log(
        `Refund processed successfully for order ${order.orderNumber}`,
      );
      return refundPayment;
    });
  }

  /**
   * 模拟第三方支付（开发阶段使用）
   */
  async simulateThirdPartyPayment(
    paymentMethod: string,
    amount: number,
  ): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    // 模拟支付处理时间
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟支付成功率（95%）
    const isSuccess = Math.random() > 0.05;

    if (isSuccess) {
      const transactionId = `${paymentMethod}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      return {
        success: true,
        transactionId,
      };
    } else {
      return {
        success: false,
        error: 'Payment failed due to insufficient funds or network error',
      };
    }
  }

  /**
   * 验证第三方支付回调
   */
  async verifyPaymentCallback(
    thirdPartyId: string,
    amount: number,
    orderId: string,
  ): Promise<boolean> {
    // 这里应该调用第三方支付平台的API来验证支付结果
    // 目前返回模拟结果
    this.logger.log(`Verifying payment callback for order ${orderId}`);

    // 模拟验证逻辑
    return Boolean(thirdPartyId && amount > 0 && orderId);
  }
}
