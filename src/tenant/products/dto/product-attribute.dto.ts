import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsEnum,
  IsNumber,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ProductAttributeType } from '@prisma/client';

export class CreateProductAttributeOptionDto {
  @ApiProperty({ description: '选项名称', example: '大杯' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '附加价格', example: 5.0 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  price: number;

  @ApiPropertyOptional({ description: '排序权重', default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  sort?: number = 0;
}

export class CreateProductAttributeDto {
  @ApiProperty({ description: '属性类型', enum: ProductAttributeType })
  @IsEnum(ProductAttributeType)
  type: ProductAttributeType;

  @ApiProperty({ description: '属性名称', example: '杯型' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({ description: '是否必选', default: false })
  @IsOptional()
  @IsBoolean()
  required?: boolean = false;

  @ApiPropertyOptional({ description: '是否可多选', default: false })
  @IsOptional()
  @IsBoolean()
  multiple?: boolean = false;

  @ApiPropertyOptional({ description: '排序权重', default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  sort?: number = 0;

  @ApiProperty({
    description: '属性选项列表',
    type: [CreateProductAttributeOptionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateProductAttributeOptionDto)
  options: CreateProductAttributeOptionDto[];
}

export class UpdateProductAttributeDto {
  @ApiPropertyOptional({ description: '属性名称' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({ description: '是否必选' })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @ApiPropertyOptional({ description: '是否可多选' })
  @IsOptional()
  @IsBoolean()
  multiple?: boolean;

  @ApiPropertyOptional({ description: '排序权重' })
  @IsOptional()
  @IsInt()
  @Min(0)
  sort?: number;
}

export class UpdateProductAttributeOptionDto {
  @ApiPropertyOptional({ description: '选项名称' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({ description: '附加价格' })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  price?: number;

  @ApiPropertyOptional({ description: '排序权重' })
  @IsOptional()
  @IsInt()
  @Min(0)
  sort?: number;
}
