import { IsOptional, IsEnum, IsString, IsUUID } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { GeneralStatus } from '@prisma/client';

export class ProductQueryDto {
  @ApiPropertyOptional({ description: '分类ID' })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({ description: '商品状态', enum: GeneralStatus })
  @IsOptional()
  @IsEnum(GeneralStatus)
  status?: GeneralStatus;

  @ApiPropertyOptional({ description: '商品名称搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '是否包含属性信息', default: false })
  @IsOptional()
  includeAttributes?: boolean;
}
