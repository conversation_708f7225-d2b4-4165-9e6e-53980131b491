import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { ProductsService } from './services/products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductQueryDto } from './dto/product-query.dto';
import { CreateProductAttributeDto } from './dto/product-attribute.dto';
import { TenantJwtAuthGuard } from '../auth/guards/tenant-jwt-auth.guard';

@ApiTags('商品管理')
@ApiBearerAuth()
@UseGuards(TenantJwtAuthGuard)
@Controller('tenant/products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @ApiOperation({ summary: '创建商品' })
  @ApiResponse({ status: 201, description: '商品创建成功' })
  @Post()
  create(@Body() createProductDto: CreateProductDto, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.productsService.create(tenantId, createProductDto);
  }

  @ApiOperation({ summary: '获取商品列表' })
  @ApiResponse({ status: 200, description: '获取商品列表成功' })
  @Get()
  findAll(@Query() queryDto: ProductQueryDto, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.productsService.findAll(tenantId, queryDto);
  }

  @ApiOperation({ summary: '获取商品详情' })
  @ApiResponse({ status: 200, description: '获取商品详情成功' })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.productsService.findOne(tenantId, id);
  }

  @ApiOperation({ summary: '更新商品信息' })
  @ApiResponse({ status: 200, description: '商品更新成功' })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @Request() req,
  ) {
    const tenantId = req.user.tenantId;
    return this.productsService.update(tenantId, id, updateProductDto);
  }

  @ApiOperation({ summary: '切换商品状态' })
  @ApiResponse({ status: 200, description: '状态切换成功' })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @Patch(':id/toggle-status')
  toggleStatus(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.productsService.toggleStatus(tenantId, id);
  }

  @ApiOperation({ summary: '更新商品库存' })
  @ApiResponse({ status: 200, description: '库存更新成功' })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @ApiParam({ name: 'id', description: '商品ID' })
  @ApiParam({ name: 'stock', description: '新库存数量' })
  @Patch(':id/stock/:stock')
  updateStock(
    @Param('id') id: string,
    @Param('stock') stock: string,
    @Request() req,
  ) {
    const tenantId = req.user.tenantId;
    return this.productsService.updateStock(tenantId, id, parseInt(stock));
  }

  @ApiOperation({ summary: '为商品添加属性' })
  @ApiResponse({ status: 201, description: '属性添加成功' })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @Post(':id/attributes')
  addAttribute(
    @Param('id') id: string,
    @Body() createAttributeDto: CreateProductAttributeDto,
    @Request() req,
  ) {
    const tenantId = req.user.tenantId;
    return this.productsService.addAttribute(tenantId, id, createAttributeDto);
  }

  @ApiOperation({ summary: '删除商品' })
  @ApiResponse({ status: 200, description: '商品删除成功' })
  @ApiResponse({ status: 404, description: '商品不存在' })
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    const tenantId = req.user.tenantId;
    return this.productsService.remove(tenantId, id);
  }
}
