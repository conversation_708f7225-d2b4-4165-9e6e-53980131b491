import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { Product, GeneralStatus } from '@prisma/client';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { ProductQueryDto } from '../dto/product-query.dto';
import { CreateProductAttributeDto } from '../dto/product-attribute.dto';

@Injectable()
export class ProductsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 创建商品
   * @param tenantId 租户ID
   * @param createProductDto 创建商品数据
   * @returns 返回创建的商品信息
   */
  async create(
    tenantId: string,
    createProductDto: CreateProductDto,
  ): Promise<Product> {
    // 验证分类是否存在（如果提供了分类ID）
    if (createProductDto.categoryId) {
      const category = await this.prisma.category.findFirst({
        where: {
          id: createProductDto.categoryId,
          tenantId,
          deletedAt: null,
        },
      });

      if (!category) {
        throw new NotFoundException('分类不存在');
      }
    }

    return this.prisma.product.create({
      data: {
        ...createProductDto,
        tenantId,
      },
      include: {
        category: true,
        _count: {
          select: {
            attributes: true,
          },
        },
      },
    });
  }

  /**
   * 查询租户的所有商品
   * @param tenantId 租户ID
   * @param queryDto 查询条件
   * @returns 返回商品列表
   */
  async findAll(
    tenantId: string,
    queryDto: ProductQueryDto = {},
  ): Promise<Product[]> {
    const { categoryId, status, search, includeAttributes } = queryDto;

    const where: any = {
      tenantId,
      deletedAt: null,
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    const include: any = {
      category: true,
      _count: {
        select: {
          attributes: true,
        },
      },
    };

    if (includeAttributes) {
      include.attributes = {
        include: {
          options: {
            where: {
              status: GeneralStatus.ACTIVE,
            },
            orderBy: { sort: 'desc' },
          },
        },
        orderBy: { sort: 'desc' },
      };
    }

    return this.prisma.product.findMany({
      where,
      orderBy: [{ sort: 'desc' }, { createdAt: 'desc' }],
      include,
    });
  }

  /**
   * 根据ID查询商品
   * @param tenantId 租户ID
   * @param id 商品ID
   * @param includeAttributes 是否包含属性信息
   * @returns 返回商品信息
   */
  async findOne(
    tenantId: string,
    id: string,
    includeAttributes = true,
  ): Promise<Product> {
    const include: any = {
      category: true,
      _count: {
        select: {
          attributes: true,
        },
      },
    };

    if (includeAttributes) {
      include.attributes = {
        include: {
          options: {
            where: {
              status: GeneralStatus.ACTIVE,
            },
            orderBy: { sort: 'desc' },
          },
        },
        orderBy: { sort: 'desc' },
      };
    }

    const product = await this.prisma.product.findFirst({
      where: {
        id,
        tenantId,
        deletedAt: null,
      },
      include,
    });

    if (!product) {
      throw new NotFoundException('商品不存在');
    }

    return product;
  }

  /**
   * 更新商品信息
   * @param tenantId 租户ID
   * @param id 商品ID
   * @param updateProductDto 更新数据
   * @returns 返回更新后的商品信息
   */
  async update(
    tenantId: string,
    id: string,
    updateProductDto: UpdateProductDto,
  ): Promise<Product> {
    await this.findOne(tenantId, id, false); // 验证商品存在

    // 验证分类是否存在（如果更新了分类ID）
    if (updateProductDto.categoryId) {
      const category = await this.prisma.category.findFirst({
        where: {
          id: updateProductDto.categoryId,
          tenantId,
          deletedAt: null,
        },
      });

      if (!category) {
        throw new NotFoundException('分类不存在');
      }
    }

    return this.prisma.product.update({
      where: { id },
      data: updateProductDto,
      include: {
        category: true,
        attributes: {
          include: {
            options: {
              where: {
                status: GeneralStatus.ACTIVE,
              },
              orderBy: { sort: 'desc' },
            },
          },
          orderBy: { sort: 'desc' },
        },
      },
    });
  }

  /**
   * 删除商品（软删除）
   * @param tenantId 租户ID
   * @param id 商品ID
   * @returns 返回删除结果
   */
  async remove(tenantId: string, id: string): Promise<Product> {
    await this.findOne(tenantId, id, false); // 验证商品存在

    // TODO: 检查是否有关联的订单项
    // const orderItemCount = await this.prisma.orderItem.count({
    //   where: {
    //     productId: id,
    //   },
    // });

    // if (orderItemCount > 0) {
    //   throw new BadRequestException('该商品已被订购，无法删除');
    // }

    return this.prisma.product.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  /**
   * 切换商品状态
   * @param tenantId 租户ID
   * @param id 商品ID
   * @returns 返回更新后的商品信息
   */
  async toggleStatus(tenantId: string, id: string): Promise<Product> {
    const product = await this.findOne(tenantId, id, false);

    const newStatus =
      product.status === GeneralStatus.ACTIVE
        ? GeneralStatus.INACTIVE
        : GeneralStatus.ACTIVE;

    return this.prisma.product.update({
      where: { id },
      data: { status: newStatus },
      include: {
        category: true,
      },
    });
  }

  /**
   * 为商品添加属性
   * @param tenantId 租户ID
   * @param productId 商品ID
   * @param createAttributeDto 属性数据
   * @returns 返回创建的属性信息
   */
  async addAttribute(
    tenantId: string,
    productId: string,
    createAttributeDto: CreateProductAttributeDto,
  ) {
    // 验证商品存在
    await this.findOne(tenantId, productId, false);

    return this.prisma.productAttribute.create({
      data: {
        productId,
        type: createAttributeDto.type,
        name: createAttributeDto.name,
        required: createAttributeDto.required,
        multiple: createAttributeDto.multiple,
        sort: createAttributeDto.sort,
        options: {
          create: createAttributeDto.options,
        },
      },
      include: {
        options: {
          where: {
            status: GeneralStatus.ACTIVE,
          },
          orderBy: { sort: 'desc' },
        },
      },
    });
  }

  /**
   * 更新商品库存
   * @param tenantId 租户ID
   * @param id 商品ID
   * @param stock 新库存数量
   * @returns 返回更新后的商品信息
   */
  async updateStock(
    tenantId: string,
    id: string,
    stock: number,
  ): Promise<Product> {
    await this.findOne(tenantId, id, false); // 验证商品存在

    if (stock < 0) {
      throw new BadRequestException('库存数量不能为负数');
    }

    return this.prisma.product.update({
      where: { id },
      data: { stock },
      include: {
        category: true,
      },
    });
  }
}
