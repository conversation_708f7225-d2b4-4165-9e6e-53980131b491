import { Module } from '@nestjs/common';
import { TenantAuthModule } from './auth/tenant-auth.module';
import { PrismaModule } from '@/common/prisma/prisma.module';
import { TenantUsersModule } from './users/tenant-users.module';
import { CategoriesModule } from './categories/categories.module';
import { ProductsModule } from './products/products.module';
import { OrdersModule } from './orders/orders.module';

@Module({
  imports: [
    TenantAuthModule,
    PrismaModule,
    TenantUsersModule,
    CategoriesModule,
    ProductsModule,
    OrdersModule,
  ],
  providers: [],
  exports: [],
})
export class TenantModule {}
