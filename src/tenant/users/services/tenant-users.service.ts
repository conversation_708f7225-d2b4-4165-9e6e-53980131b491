import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/common/prisma/prisma.service';
import { TenantUser } from '@prisma/client';
import * as bcrypt from 'bcrypt';

@Injectable()
export class TenantUsersService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 根据邮箱查找用户
   * @param email 用户邮箱
   * @returns 返回用户信息，或在找不到时返回 null
   */
  async findOneByEmail(
    email: string,
  ): Promise<TenantUser | null> {
    return this.prisma.tenantUser.findUnique({
      where: {
        email,
        deletedAt: null,
      },

    });
  }

  /**
   * 验证用户密码
   * @param plainPassword 明文密码
   * @param hashedPassword 哈希后的密码
   * @returns 如果密码匹配则返回 true，否则返回 false
   */
  async validatePassword(
    plainPassword: string,
    hashedPassword,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
